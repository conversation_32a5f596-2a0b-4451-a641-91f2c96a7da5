-- Create media table for Payload CMS
CREATE TABLE IF NOT EXISTS public.media (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  alt text,
  caption text,
  category text,
  prefix text,
  updated_at timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  url text,
  thumbnail_u_r_l text,
  filename text,
  mime_type text,
  filesize integer,
  width integer,
  height integer,
  focal_x integer,
  focal_y integer,
  sizes_thumbnail_url text,
  sizes_thumbnail_width integer,
  sizes_thumbnail_height integer,
  sizes_thumbnail_mime_type text,
  sizes_thumbnail_filesize integer,
  sizes_thumbnail_filename text,
  sizes_card_url text,
  sizes_card_width integer,
  sizes_card_height integer,
  sizes_card_mime_type text,
  sizes_card_filesize integer,
  sizes_card_filename text,
  sizes_tablet_url text,
  sizes_tablet_width integer,
  sizes_tablet_height integer,
  sizes_tablet_mime_type text,
  sizes_tablet_filesize integer,
  sizes_tablet_filename text,
  sizes_desktop_url text,
  sizes_desktop_width integer,
  sizes_desktop_height integer,
  sizes_desktop_mime_type text,
  sizes_desktop_filesize integer,
  sizes_desktop_filename text
);

-- Enable RLS
ALTER TABLE public.media ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Enable read access for all users" ON public.media
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.media
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users only" ON public.media
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users only" ON public.media
  FOR DELETE USING (auth.role() = 'authenticated');

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_media_created_at ON public.media(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_media_category ON public.media(category);
CREATE INDEX IF NOT EXISTS idx_media_mime_type ON public.media(mime_type);

-- Insert a test record to verify the table works
INSERT INTO public.media (alt, caption, category, filename, mime_type, filesize, url) 
VALUES ('Test Image', 'Test media upload', 'test', 'test.jpg', 'image/jpeg', 1024, 'https://example.com/test.jpg')
ON CONFLICT DO NOTHING;
