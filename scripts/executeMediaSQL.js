import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const supabaseUrl = 'https://ueiouusrrngdjrcoctem.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlaW91dXNycm5nZGpyY29jdGVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MzU2ODUsImV4cCI6MjA2NTExMTY4NX0.eeBn7QFGZUBOfVR5xeDaL_yo4cfVjWhgGDjBRbiYWCU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function executeMediaSQL() {
  console.log('🔧 Creating media table in Supabase...\n');

  try {
    // Read the SQL file
    const sqlContent = readFileSync(join(__dirname, 'createMediaTable.sql'), 'utf8');
    
    // Split SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Executing ${statements.length} SQL statements...\n`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`${i + 1}. ${statement.substring(0, 50)}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error) {
          console.log(`   ❌ Error: ${error.message}`);
        } else {
          console.log(`   ✅ Success`);
        }
      } catch (err) {
        console.log(`   ❌ Error: ${err.message}`);
      }
    }

    // Test the table
    console.log('\n🧪 Testing media table...');
    const { data, error } = await supabase
      .from('media')
      .select('*')
      .limit(1);

    if (error) {
      console.log('❌ Error testing table:', error.message);
    } else {
      console.log('✅ Media table is working!');
      console.log(`📊 Found ${data.length} records`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

executeMediaSQL();
