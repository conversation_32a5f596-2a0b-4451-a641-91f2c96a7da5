import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

async function inspectTableStructures() {
  console.log('🔍 Inspecting actual table structures...\n')
  
  const tables = ['doctors', 'user_profiles', 'appointments', 'services', 'time_slots']
  
  for (const table of tables) {
    console.log(`📋 Table: ${table}`)
    
    // Get table structure by trying to insert empty data and seeing the error
    try {
      const { error } = await supabase
        .from(table)
        .insert({})
        .select()
      
      if (error) {
        console.log(`   Structure hints from error: ${error.message}`)
        
        // Extract column names from error message
        const columnMatches = error.message.match(/column "([^"]+)"/g)
        if (columnMatches) {
          const columns = columnMatches.map(match => match.replace(/column "|"/g, ''))
          console.log(`   Detected columns: ${columns.join(', ')}`)
        }
      }
    } catch (err) {
      console.log(`   Error: ${err.message}`)
    }
    
    // Try to get one record to see actual structure
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1)
      
      if (!error && data && data.length > 0) {
        console.log(`   Actual columns: ${Object.keys(data[0]).join(', ')}`)
      } else if (!error) {
        console.log(`   Table exists but is empty`)
      }
    } catch (err) {
      console.log(`   Cannot read table: ${err.message}`)
    }
    
    console.log('')
  }
}

inspectTableStructures().catch(console.error)
