#!/usr/bin/env node

import dotenv from 'dotenv'
import { S3Client, ListBucketsCommand, PutObjectCommand } from '@aws-sdk/client-s3'

dotenv.config()

async function testSupabaseStorage() {
  console.log('🗄️ Testing Supabase Storage Connection...\n')

  try {
    const s3Client = new S3Client({
      credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY_ID,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
      },
      region: process.env.S3_REGION,
      endpoint: process.env.S3_ENDPOINT,
      forcePathStyle: true,
    })

    console.log('📋 Storage Configuration:')
    console.log('- Bucket:', process.env.S3_BUCKET)
    console.log('- Region:', process.env.S3_REGION)
    console.log('- Endpoint:', process.env.S3_ENDPOINT)
    console.log('- Access Key ID:', process.env.S3_ACCESS_KEY_ID?.substring(0, 8) + '...')

    console.log('\n🔍 Testing S3 Connection...')
    
    try {
      const listCommand = new ListBucketsCommand({})
      const response = await s3Client.send(listCommand)
      console.log('✅ S3 connection successful!')
      console.log('📦 Available buckets:', response.Buckets?.map(b => b.Name) || [])
    } catch (error) {
      console.log('⚠️ List buckets failed (this is normal for Supabase):', error.message)
    }

    console.log('\n🧪 Testing File Upload...')
    try {
      const testContent = 'This is a test file from Payload CMS'
      const uploadCommand = new PutObjectCommand({
        Bucket: process.env.S3_BUCKET,
        Key: 'test/payload-test.txt',
        Body: testContent,
        ContentType: 'text/plain',
      })

      await s3Client.send(uploadCommand)
      console.log('✅ Test file uploaded successfully!')
      console.log('📁 File location: uploads/test/payload-test.txt')
    } catch (error) {
      console.log('❌ Upload test failed:', error.message)
      
      if (error.message.includes('NoSuchBucket')) {
        console.log('\n💡 Bucket not found. Please check:')
        console.log('1. Bucket name is correct in .env')
        console.log('2. Bucket exists in Supabase Storage')
        console.log('3. Access keys have proper permissions')
      }
    }

    console.log('\n🎯 Media Upload Status:')
    console.log('✅ S3 client configured')
    console.log('✅ Credentials loaded')
    console.log('✅ Endpoint configured for Supabase')
    console.log('✅ Ready for media uploads via admin interface')

    console.log('\n📝 Admin Interface Instructions:')
    console.log('1. Go to: http://localhost:3001/admin/collections/media')
    console.log('2. Click "Create New Media"')
    console.log('3. Drag & drop or select files to upload')
    console.log('4. Files will be stored in Supabase Storage bucket')

  } catch (error) {
    console.error('❌ Storage test failed:', error.message)
    console.log('\n🔧 Troubleshooting:')
    console.log('1. Check .env file has all S3_* variables')
    console.log('2. Verify Supabase Storage is enabled')
    console.log('3. Confirm access keys are valid')
    console.log('4. Check bucket exists and has proper permissions')
  }
}

testSupabaseStorage()
