import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkMediaTable() {
  console.log('🔍 Checking media table...\n');

  try {
    // Check if media table exists
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'media');

    if (tablesError) {
      console.error('❌ Error checking tables:', tablesError);
      return;
    }

    if (!tables || tables.length === 0) {
      console.log('❌ Media table does not exist in Supabase');
      console.log('📝 Creating media table...\n');
      await createMediaTable();
    } else {
      console.log('✅ Media table exists');
      await checkMediaSchema();
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function createMediaTable() {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS public.media (
      id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
      alt text,
      caption text,
      category text,
      prefix text,
      updated_at timestamp with time zone DEFAULT now(),
      created_at timestamp with time zone DEFAULT now(),
      url text,
      thumbnail_u_r_l text,
      filename text,
      mime_type text,
      filesize integer,
      width integer,
      height integer,
      focal_x integer,
      focal_y integer,
      sizes_thumbnail_url text,
      sizes_thumbnail_width integer,
      sizes_thumbnail_height integer,
      sizes_thumbnail_mime_type text,
      sizes_thumbnail_filesize integer,
      sizes_thumbnail_filename text,
      sizes_card_url text,
      sizes_card_width integer,
      sizes_card_height integer,
      sizes_card_mime_type text,
      sizes_card_filesize integer,
      sizes_card_filename text,
      sizes_tablet_url text,
      sizes_tablet_width integer,
      sizes_tablet_height integer,
      sizes_tablet_mime_type text,
      sizes_tablet_filesize integer,
      sizes_tablet_filename text,
      sizes_desktop_url text,
      sizes_desktop_width integer,
      sizes_desktop_height integer,
      sizes_desktop_mime_type text,
      sizes_desktop_filesize integer,
      sizes_desktop_filename text
    );

    -- Enable RLS
    ALTER TABLE public.media ENABLE ROW LEVEL SECURITY;

    -- Create RLS policies
    CREATE POLICY "Enable read access for all users" ON public.media
      FOR SELECT USING (true);

    CREATE POLICY "Enable insert for authenticated users only" ON public.media
      FOR INSERT WITH CHECK (auth.role() = 'authenticated');

    CREATE POLICY "Enable update for authenticated users only" ON public.media
      FOR UPDATE USING (auth.role() = 'authenticated');

    CREATE POLICY "Enable delete for authenticated users only" ON public.media
      FOR DELETE USING (auth.role() = 'authenticated');

    -- Create indexes
    CREATE INDEX IF NOT EXISTS idx_media_created_at ON public.media(created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_media_category ON public.media(category);
    CREATE INDEX IF NOT EXISTS idx_media_mime_type ON public.media(mime_type);
  `;

  try {
    const { error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      console.error('❌ Error creating media table:', error);
      return;
    }

    console.log('✅ Media table created successfully!');
    console.log('✅ RLS policies enabled');
    console.log('✅ Indexes created');
    
  } catch (error) {
    console.error('❌ Error creating media table:', error.message);
  }
}

async function checkMediaSchema() {
  try {
    const { data: columns, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_schema', 'public')
      .eq('table_name', 'media')
      .order('ordinal_position');

    if (error) {
      console.error('❌ Error checking schema:', error);
      return;
    }

    console.log('📋 Media table schema:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(required)'}`);
    });

  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
  }
}

checkMediaTable();
