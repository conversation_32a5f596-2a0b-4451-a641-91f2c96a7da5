import pg from 'pg';

const { Client } = pg;

// Database connection from your .env
const client = new Client({
  connectionString: '****************************************************************/postgres'
});

async function fixMediaTable() {
  console.log('🔧 Fixing media table for Payload CMS...\n');

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check if media table exists
    const checkTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'media'
      );
    `);

    if (!checkTable.rows[0].exists) {
      console.log('📝 Creating media table...');
      
      // Create the media table with all required columns
      await client.query(`
        CREATE TABLE public.media (
          id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
          alt text,
          caption text,
          category text,
          prefix text,
          updated_at timestamp with time zone DEFAULT now(),
          created_at timestamp with time zone DEFAULT now(),
          url text,
          thumbnail_u_r_l text,
          filename text,
          mime_type text,
          filesize integer,
          width integer,
          height integer,
          focal_x integer,
          focal_y integer,
          sizes_thumbnail_url text,
          sizes_thumbnail_width integer,
          sizes_thumbnail_height integer,
          sizes_thumbnail_mime_type text,
          sizes_thumbnail_filesize integer,
          sizes_thumbnail_filename text,
          sizes_card_url text,
          sizes_card_width integer,
          sizes_card_height integer,
          sizes_card_mime_type text,
          sizes_card_filesize integer,
          sizes_card_filename text,
          sizes_tablet_url text,
          sizes_tablet_width integer,
          sizes_tablet_height integer,
          sizes_tablet_mime_type text,
          sizes_tablet_filesize integer,
          sizes_tablet_filename text,
          sizes_desktop_url text,
          sizes_desktop_width integer,
          sizes_desktop_height integer,
          sizes_desktop_mime_type text,
          sizes_desktop_filesize integer,
          sizes_desktop_filename text
        );
      `);

      console.log('✅ Media table created');

      // Create indexes
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_media_created_at ON public.media(created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_media_category ON public.media(category);
        CREATE INDEX IF NOT EXISTS idx_media_mime_type ON public.media(mime_type);
      `);

      console.log('✅ Indexes created');

    } else {
      console.log('✅ Media table already exists');
    }

    // Test the table with the exact query from the error
    console.log('\n🧪 Testing the exact query from Payload...');
    
    const testQuery = `
      SELECT "id", "alt", "caption", "category", "prefix", "updated_at", "created_at", "url", 
             "thumbnail_u_r_l", "filename", "mime_type", "filesize", "width", "height", 
             "focal_x", "focal_y", "sizes_thumbnail_url", "sizes_thumbnail_width", 
             "sizes_thumbnail_height", "sizes_thumbnail_mime_type", "sizes_thumbnail_filesize", 
             "sizes_thumbnail_filename", "sizes_card_url", "sizes_card_width", "sizes_card_height", 
             "sizes_card_mime_type", "sizes_card_filesize", "sizes_card_filename", 
             "sizes_tablet_url", "sizes_tablet_width", "sizes_tablet_height", 
             "sizes_tablet_mime_type", "sizes_tablet_filesize", "sizes_tablet_filename", 
             "sizes_desktop_url", "sizes_desktop_width", "sizes_desktop_height", 
             "sizes_desktop_mime_type", "sizes_desktop_filesize", "sizes_desktop_filename" 
      FROM "media" "media" 
      ORDER BY "media"."created_at" DESC 
      LIMIT 10
    `;

    const result = await client.query(testQuery);
    console.log('✅ Query successful!');
    console.log(`📊 Found ${result.rows.length} records`);

    // Insert a test record if table is empty
    if (result.rows.length === 0) {
      console.log('\n📝 Adding test record...');
      await client.query(`
        INSERT INTO public.media (alt, caption, category, filename, mime_type, filesize, url) 
        VALUES ('Test Image', 'Test media upload', 'test', 'test.jpg', 'image/jpeg', 1024, 'https://example.com/test.jpg')
      `);
      console.log('✅ Test record added');
    }

    console.log('\n🎉 Media table is ready for Payload CMS!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

fixMediaTable();
