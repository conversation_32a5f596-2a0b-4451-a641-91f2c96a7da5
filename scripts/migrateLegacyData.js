import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

dotenv.config()

// Use service role key for bypassing R<PERSON> during migration
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY
)

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

async function migrateExistingData() {
  console.log('🔄 Starting data migration and synchronization...\n')
  
  try {
    // Step 1: Analyze existing data
    console.log('📊 Step 1: Analyzing existing data...')
    
    const { data: services } = await supabase.from('services').select('*')
    const { data: timeSlots } = await supabase.from('time_slots').select('*')
    const { data: appointments } = await supabase.from('appointments').select('*')
    const { data: doctors } = await supabase.from('doctors').select('*')
    const { data: userProfiles } = await supabase.from('user_profiles').select('*')
    
    console.log(`   Services: ${services?.length || 0} records`)
    console.log(`   Time Slots: ${timeSlots?.length || 0} records`)
    console.log(`   Appointments: ${appointments?.length || 0} records`)
    console.log(`   Doctors: ${doctors?.length || 0} records`)
    console.log(`   User Profiles: ${userProfiles?.length || 0} records`)
    console.log('')
    
    // Step 2: Create sample data for testing (if tables are empty)
    if (doctors?.length === 0) {
      console.log('➕ Step 2: Creating sample doctors for testing...')
      
      const sampleDoctors = [
        {
          name: 'Dr. Sarah Johnson',
          specialization: 'Dermatology',
          bio: 'Experienced dermatologist specializing in cosmetic procedures',
          is_active: true,
          consultation_fee: 1000
        },
        {
          name: 'Dr. Michael Chen',
          specialization: 'Aesthetic Medicine',
          bio: 'Expert in non-invasive aesthetic treatments',
          is_active: true,
          consultation_fee: 1200
        }
      ]
      
      for (const doctor of sampleDoctors) {
        const { data, error } = await supabaseAdmin
          .from('doctors')
          .insert(doctor)
          .select()
        
        if (error) {
          console.log(`   ❌ Error creating doctor ${doctor.name}: ${error.message}`)
        } else {
          console.log(`   ✅ Created doctor: ${data[0].name} (ID: ${data[0].id})`)
        }
      }
      console.log('')
    }
    
    if (userProfiles?.length === 0) {
      console.log('➕ Step 3: Creating sample user profiles for testing...')
      
      const sampleUsers = [
        {
          first_name: 'Emma',
          last_name: 'Wilson',
          phone: '+66-81-234-5678',
          date_of_birth: '1992-05-15',
          gender: 'female'
        },
        {
          first_name: 'James',
          last_name: 'Brown',
          phone: '+66-82-345-6789',
          date_of_birth: '1988-09-22',
          gender: 'male'
        }
      ]
      
      for (const user of sampleUsers) {
        const { data, error } = await supabaseAdmin
          .from('user_profiles')
          .insert(user)
          .select()
        
        if (error) {
          console.log(`   ❌ Error creating user ${user.first_name}: ${error.message}`)
        } else {
          console.log(`   ✅ Created user: ${data[0].first_name} ${data[0].last_name} (ID: ${data[0].id})`)
        }
      }
      console.log('')
    }
    
    // Step 4: Create sample appointments with relationships
    console.log('➕ Step 4: Creating sample appointments with relationships...')
    
    // Get fresh data after potential inserts
    const { data: updatedDoctors } = await supabase.from('doctors').select('*')
    const { data: updatedUsers } = await supabase.from('user_profiles').select('*')
    const { data: updatedServices } = await supabase.from('services').select('*')
    const { data: updatedTimeSlots } = await supabase.from('time_slots').select('*')
    
    if (updatedDoctors?.length > 0 && updatedUsers?.length > 0 && 
        updatedServices?.length > 0 && updatedTimeSlots?.length > 0) {
      
      const sampleAppointments = [
        {
          patient_id: updatedUsers[0].id,
          doctor_id: updatedDoctors[0].id,
          service_id: updatedServices[0].id,
          slot_id: updatedTimeSlots[0].id,
          status: 'scheduled',
          notes: 'Initial consultation for HydraFacial treatment',
          total_amount: updatedServices[0].base_price
        },
        {
          patient_id: updatedUsers[1] ? updatedUsers[1].id : updatedUsers[0].id,
          doctor_id: updatedDoctors[1] ? updatedDoctors[1].id : updatedDoctors[0].id,
          service_id: updatedServices[1] ? updatedServices[1].id : updatedServices[0].id,
          slot_id: updatedTimeSlots[1] ? updatedTimeSlots[1].id : updatedTimeSlots[0].id,
          status: 'confirmed',
          notes: 'Follow-up appointment',
          total_amount: updatedServices[1] ? updatedServices[1].base_price : updatedServices[0].base_price
        }
      ]
      
      for (const appointment of sampleAppointments) {
        const { data, error } = await supabaseAdmin
          .from('appointments')
          .insert(appointment)
          .select()
        
        if (error) {
          console.log(`   ❌ Error creating appointment: ${error.message}`)
        } else {
          console.log(`   ✅ Created appointment (ID: ${data[0].id}) - ${data[0].status}`)
        }
      }
    } else {
      console.log('   ⚠️ Skipping appointments - missing required related data')
    }
    console.log('')
    
    // Step 5: Test relationships and data integrity
    console.log('🔗 Step 5: Testing relationships and data integrity...')
    
    const { data: appointmentsWithRelations, error: relationError } = await supabase
      .from('appointments')
      .select(`
        *,
        user_profiles!appointments_patient_id_fkey(first_name, last_name, phone),
        doctors!appointments_doctor_id_fkey(name, specialization),
        services!appointments_service_id_fkey(name, base_price, duration_minutes),
        time_slots!appointments_slot_id_fkey(date, start_time, end_time)
      `)
      .limit(3)
    
    if (relationError) {
      console.log(`   ❌ Error testing relationships: ${relationError.message}`)
    } else {
      console.log(`   ✅ Successfully loaded ${appointmentsWithRelations.length} appointments with relationships`)
      
      appointmentsWithRelations.forEach((appt, index) => {
        console.log(`   Appointment ${index + 1}:`)
        console.log(`     Patient: ${appt.user_profiles?.first_name} ${appt.user_profiles?.last_name}`)
        console.log(`     Doctor: ${appt.doctors?.name} (${appt.doctors?.specialization})`)
        console.log(`     Service: ${appt.services?.name} - ${appt.services?.base_price} THB`)
        console.log(`     Time: ${appt.time_slots?.date} ${appt.time_slots?.start_time}`)
        console.log(`     Status: ${appt.status}`)
        console.log('')
      })
    }
    
    // Step 6: Performance benchmarks
    console.log('⚡ Step 6: Performance benchmarks...')
    
    const startTime = Date.now()
    const [servicesResult, appointmentsResult, doctorsResult] = await Promise.all([
      supabase.from('services').select('*'),
      supabase.from('appointments').select(`
        *,
        user_profiles!appointments_patient_id_fkey(first_name, last_name),
        doctors!appointments_doctor_id_fkey(name, specialization),
        services!appointments_service_id_fkey(name, base_price)
      `),
      supabase.from('doctors').select('*')
    ])
    const endTime = Date.now()
    
    console.log(`   ✅ Parallel queries completed in ${endTime - startTime}ms`)
    console.log(`   Services: ${servicesResult.data?.length || 0} records`)
    console.log(`   Appointments (with relations): ${appointmentsResult.data?.length || 0} records`)
    console.log(`   Doctors: ${doctorsResult.data?.length || 0} records`)
    console.log('')
    
    console.log('🎉 Migration and testing completed successfully!')
    console.log('\n📊 Final Summary:')
    console.log(`✅ Services: ${servicesResult.data?.length || 0} records (existing data)`)
    console.log(`✅ Time Slots: ${updatedTimeSlots?.length || 0} records (existing data)`)
    console.log(`✅ Doctors: ${updatedDoctors?.length || 0} records (created for testing)`)
    console.log(`✅ User Profiles: ${updatedUsers?.length || 0} records (created for testing)`)
    console.log(`✅ Appointments: ${appointmentsResult.data?.length || 0} records (created with relationships)`)
    console.log(`✅ Relationships: Working correctly`)
    console.log(`✅ Performance: ${endTime - startTime}ms for complex queries`)
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message)
  }
}

migrateExistingData().catch(console.error)
