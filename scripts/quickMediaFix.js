import pg from 'pg';

const { Client } = pg;

const client = new Client({
  connectionString: '****************************************************************/postgres'
});

async function quickFix() {
  try {
    await client.connect();
    
    // Drop and recreate the media table to ensure it's correct
    console.log('🔄 Recreating media table...');
    
    await client.query('DROP TABLE IF EXISTS public.media CASCADE;');
    
    await client.query(`
      CREATE TABLE public.media (
        id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
        alt text,
        caption text,
        category text,
        prefix text,
        updated_at timestamp with time zone DEFAULT now(),
        created_at timestamp with time zone DEFAULT now(),
        url text,
        thumbnail_u_r_l text,
        filename text,
        mime_type text,
        filesize integer,
        width integer,
        height integer,
        focal_x integer,
        focal_y integer,
        sizes_thumbnail_url text,
        sizes_thumbnail_width integer,
        sizes_thumbnail_height integer,
        sizes_thumbnail_mime_type text,
        sizes_thumbnail_filesize integer,
        sizes_thumbnail_filename text,
        sizes_card_url text,
        sizes_card_width integer,
        sizes_card_height integer,
        sizes_card_mime_type text,
        sizes_card_filesize integer,
        sizes_card_filename text,
        sizes_tablet_url text,
        sizes_tablet_width integer,
        sizes_tablet_height integer,
        sizes_tablet_mime_type text,
        sizes_tablet_filesize integer,
        sizes_tablet_filename text,
        sizes_desktop_url text,
        sizes_desktop_width integer,
        sizes_desktop_height integer,
        sizes_desktop_mime_type text,
        sizes_desktop_filesize integer,
        sizes_desktop_filename text
      );
    `);
    
    console.log('✅ Media table recreated successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
    process.exit(0);
  }
}

quickFix();
