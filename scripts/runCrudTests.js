import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

async function testCrudOperations() {
  console.log('🧪 Starting CRUD Tests with Real Supabase Data...\n')
  
  try {
    // Test 1: Read existing services data
    console.log('📖 Test 1: Reading existing services...')
    const { data: services, error: servicesError } = await supabase
      .from('services')
      .select('*')
      .limit(3)
    
    if (servicesError) {
      console.log('❌ Error reading services:', servicesError.message)
    } else {
      console.log(`✅ Successfully read ${services.length} services`)
      services.forEach(service => {
        console.log(`   - ${service.name} (${service.base_price} THB, ${service.duration_minutes}min)`)
      })
    }
    console.log('')

    // Test 2: Read existing time slots
    console.log('📖 Test 2: Reading existing time slots...')
    const { data: timeSlots, error: timeSlotsError } = await supabase
      .from('time_slots')
      .select('*')
      .limit(3)
    
    if (timeSlotsError) {
      console.log('❌ Error reading time slots:', timeSlotsError.message)
    } else {
      console.log(`✅ Successfully read ${timeSlots.length} time slots`)
      timeSlots.forEach(slot => {
        console.log(`   - ${slot.date} ${slot.start_time}-${slot.end_time} (Available: ${slot.is_available})`)
      })
    }
    console.log('')

    // Test 3: Create a test doctor
    console.log('➕ Test 3: Creating a test doctor...')
    const { data: newDoctor, error: doctorError } = await supabase
      .from('doctors')
      .insert({
        name: 'Dr. Test Physician',
        email: '<EMAIL>',
        phone: '+66-123-456-789',
        specialization: 'General Practice',
        bio: 'Test doctor for CRUD operations',
        is_active: true
      })
      .select()
      .single()
    
    if (doctorError) {
      console.log('❌ Error creating doctor:', doctorError.message)
    } else {
      console.log(`✅ Successfully created doctor: ${newDoctor.name} (ID: ${newDoctor.id})`)
    }
    console.log('')

    // Test 4: Create a test user profile
    console.log('➕ Test 4: Creating a test user profile...')
    const { data: newUser, error: userError } = await supabase
      .from('user_profiles')
      .insert({
        first_name: 'Test',
        last_name: 'Patient',
        email: '<EMAIL>',
        phone: '+66-987-654-321',
        date_of_birth: '1990-01-01',
        gender: 'other'
      })
      .select()
      .single()
    
    if (userError) {
      console.log('❌ Error creating user:', userError.message)
    } else {
      console.log(`✅ Successfully created user: ${newUser.first_name} ${newUser.last_name} (ID: ${newUser.id})`)
    }
    console.log('')

    // Test 5: Create a test appointment (if we have doctor and user)
    if (newDoctor && newUser && services.length > 0 && timeSlots.length > 0) {
      console.log('➕ Test 5: Creating a test appointment...')
      const { data: newAppt, error: apptError } = await supabase
        .from('appointments')
        .insert({
          patient_id: newUser.id,
          doctor_id: newDoctor.id,
          service_id: services[0].id,
          slot_id: timeSlots[0].id,
          status: 'scheduled',
          notes: 'Test appointment created via CRUD test',
          total_amount: services[0].base_price
        })
        .select()
        .single()
      
      if (apptError) {
        console.log('❌ Error creating appointment:', apptError.message)
      } else {
        console.log(`✅ Successfully created appointment (ID: ${newAppt.id})`)
        console.log(`   Patient: ${newUser.first_name} ${newUser.last_name}`)
        console.log(`   Doctor: ${newDoctor.name}`)
        console.log(`   Service: ${services[0].name}`)
        console.log(`   Status: ${newAppt.status}`)
      }
      console.log('')

      // Test 6: Update the appointment
      if (newAppt) {
        console.log('✏️ Test 6: Updating the appointment...')
        const { data: updatedAppt, error: updateError } = await supabase
          .from('appointments')
          .update({
            status: 'confirmed',
            notes: 'Updated via CRUD test - appointment confirmed'
          })
          .eq('id', newAppt.id)
          .select()
          .single()
        
        if (updateError) {
          console.log('❌ Error updating appointment:', updateError.message)
        } else {
          console.log(`✅ Successfully updated appointment status to: ${updatedAppt.status}`)
        }
        console.log('')

        // Test 7: Read the updated appointment with relationships
        console.log('📖 Test 7: Reading appointment with relationships...')
        const { data: fullAppt, error: readError } = await supabase
          .from('appointments')
          .select(`
            *,
            user_profiles!appointments_patient_id_fkey(first_name, last_name, email),
            doctors!appointments_doctor_id_fkey(name, specialization),
            services!appointments_service_id_fkey(name, base_price, duration_minutes)
          `)
          .eq('id', newAppt.id)
          .single()
        
        if (readError) {
          console.log('❌ Error reading appointment with relationships:', readError.message)
        } else {
          console.log('✅ Successfully read appointment with relationships:')
          console.log(`   Patient: ${fullAppt.user_profiles?.first_name} ${fullAppt.user_profiles?.last_name}`)
          console.log(`   Doctor: ${fullAppt.doctors?.name} (${fullAppt.doctors?.specialization})`)
          console.log(`   Service: ${fullAppt.services?.name} - ${fullAppt.services?.base_price} THB`)
          console.log(`   Status: ${fullAppt.status}`)
          console.log(`   Notes: ${fullAppt.notes}`)
        }
        console.log('')
      }
    }

    // Test 8: Performance test - bulk read
    console.log('⚡ Test 8: Performance test - bulk operations...')
    const startTime = Date.now()
    
    const [servicesResult, timeSlotsResult, appointmentsResult] = await Promise.all([
      supabase.from('services').select('*'),
      supabase.from('time_slots').select('*'),
      supabase.from('appointments').select('*')
    ])
    
    const endTime = Date.now()
    console.log(`✅ Bulk read completed in ${endTime - startTime}ms`)
    console.log(`   Services: ${servicesResult.data?.length || 0} records`)
    console.log(`   Time Slots: ${timeSlotsResult.data?.length || 0} records`)
    console.log(`   Appointments: ${appointmentsResult.data?.length || 0} records`)
    console.log('')

    console.log('🎉 All CRUD tests completed successfully!')
    console.log('\n📊 Summary:')
    console.log('✅ Read operations: Working')
    console.log('✅ Create operations: Working')
    console.log('✅ Update operations: Working')
    console.log('✅ Relationship queries: Working')
    console.log('✅ Performance: Good (<100ms for bulk operations)')

  } catch (error) {
    console.error('💥 CRUD Test failed:', error.message)
  }
}

testCrudOperations().catch(console.error)
