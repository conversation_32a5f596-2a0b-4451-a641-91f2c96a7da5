import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const targetTables = [
  'appointments',
  'doctors', 
  'services',
  'time_slots',
  'clinic_settings',
  'user_profiles',
  'admin_profiles',
  'invoices',
  'payments',
  'promotions',
  'inventory_items',
  'blog_posts',
  'cms_pages',
  'marketing_campaigns',
  'reviews',
  'analytics_events',
  'kpi_metrics'
]

function getTableSchema(tableName) {
  const query = `
    SELECT 
      column_name,
      data_type,
      is_nullable,
      column_default,
      character_maximum_length
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = '${tableName}'
    ORDER BY ordinal_position;
  `
  
  try {
    const result = execSync(
      `PGPASSWORD="Resworpp@191" psql -h aws-0-ap-southeast-1.pooler.supabase.com -p 6543 -U postgres.ueiouusrrngdjrcoctem -d postgres -t -c "${query}"`,
      { encoding: 'utf8' }
    )
    
    const rows = result.trim().split('\n').filter(row => row.trim())
    const columns = rows.map(row => {
      const parts = row.split('|').map(p => p.trim())
      return {
        column_name: parts[0],
        data_type: parts[1],
        is_nullable: parts[2],
        column_default: parts[3] === '' ? null : parts[3],
        character_maximum_length: parts[4] === '' ? null : parseInt(parts[4])
      }
    })
    
    return columns
  } catch (error) {
    console.error(`Error fetching schema for ${tableName}:`, error.message)
    return []
  }
}

function mapPostgresToPayloadField(column) {
  const { column_name, data_type, is_nullable, column_default } = column
  
  let field = {
    name: column_name,
    type: 'text',
    required: is_nullable === 'NO' && !column_default,
  }

  switch (data_type) {
    case 'integer':
    case 'bigint':
    case 'smallint':
    case 'numeric':
    case 'decimal':
    case 'real':
    case 'double precision':
      field.type = 'number'
      break
    
    case 'boolean':
      field.type = 'checkbox'
      break
    
    case 'timestamp with time zone':
    case 'timestamp without time zone':
    case 'date':
      field.type = 'date'
      break
    
    case 'text':
    case 'character varying':
    case 'varchar':
      field.type = 'text'
      break
    
    case 'jsonb':
    case 'json':
      field.type = 'json'
      break
    
    default:
      field.type = 'text'
  }

  if (column_name === 'id') {
    field.type = 'number'
    field.required = true
  }

  if (column_name.includes('email')) {
    field.type = 'email'
  }

  if (column_name.includes('password') || column_name.includes('hash') || column_name.includes('salt')) {
    field.type = 'text'
    field.admin = { hidden: true }
  }

  return field
}

function generatePayloadFields(columns) {
  return columns
    .filter(col => !['created_at', 'updated_at'].includes(col.column_name))
    .map(mapPostgresToPayloadField)
}

async function main() {
  const schemaDir = path.join(__dirname, '..', 'src', '_schema')
  
  if (!fs.existsSync(schemaDir)) {
    fs.mkdirSync(schemaDir, { recursive: true })
  }

  console.log('🔄 Generating Payload schemas from Supabase tables...\n')

  for (const tableName of targetTables) {
    console.log(`📋 Processing table: ${tableName}`)
    
    const columns = getTableSchema(tableName)
    
    if (columns.length === 0) {
      console.log(`⚠️  No columns found for ${tableName}, skipping...`)
      continue
    }
    
    const fields = generatePayloadFields(columns)
    
    const schema = {
      tableName,
      fields,
      generatedAt: new Date().toISOString()
    }
    
    const filePath = path.join(schemaDir, `${tableName}.json`)
    fs.writeFileSync(filePath, JSON.stringify(schema, null, 2))
    
    console.log(`✅ Generated schema for ${tableName} (${fields.length} fields)`)
  }

  console.log(`\n🎉 Schema generation complete! Generated ${targetTables.length} schema files.`)
}

main().catch(console.error)
