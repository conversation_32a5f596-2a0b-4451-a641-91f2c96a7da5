import { supabase } from '../src/lib/supabaseAdapter.js'

async function checkData() {
  console.log('🔍 Checking Supabase database structure and data...\n')
  
  const tables = [
    'appointments',
    'doctors', 
    'user_profiles',
    'services',
    'time_slots',
    'payments',
    'invoices',
    'reviews'
  ]
  
  for (const table of tables) {
    try {
      const { data, error, count } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true })
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`)
      } else {
        console.log(`✅ ${table}: ${count || 0} records`)
        
        if (count && count > 0) {
          const { data: sample } = await supabase
            .from(table)
            .select('*')
            .limit(1)
          
          if (sample && sample[0]) {
            console.log(`   Sample fields: ${Object.keys(sample[0]).join(', ')}`)
          }
        }
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err}`)
    }
    console.log('')
  }
}

checkData().catch(console.error)
