import pg from 'pg';

const { Client } = pg;

async function verifyMediaSystem() {
  console.log('🔍 Verifying Media System Status...\n');

  const client = new Client({
    connectionString: '****************************************************************/postgres'
  });

  try {
    await client.connect();
    console.log('✅ Database connection successful');

    // Check if media table exists and has correct structure
    const tableCheck = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'media'
      ORDER BY ordinal_position;
    `);

    if (tableCheck.rows.length === 0) {
      console.log('❌ Media table does not exist');
      return;
    }

    console.log(`✅ Media table exists with ${tableCheck.rows.length} columns`);

    // Test the exact query that was failing
    const testQuery = await client.query(`
      SELECT id, alt, caption, category, prefix, updated_at, created_at, url, 
             thumbnail_u_r_l, filename, mime_type, filesize, width, height, 
             focal_x, focal_y, sizes_thumbnail_url, sizes_thumbnail_width, 
             sizes_thumbnail_height, sizes_thumbnail_mime_type, sizes_thumbnail_filesize, 
             sizes_thumbnail_filename, sizes_card_url, sizes_card_width, sizes_card_height, 
             sizes_card_mime_type, sizes_card_filesize, sizes_card_filename, 
             sizes_tablet_url, sizes_tablet_width, sizes_tablet_height, 
             sizes_tablet_mime_type, sizes_tablet_filesize, sizes_tablet_filename, 
             sizes_desktop_url, sizes_desktop_width, sizes_desktop_height, 
             sizes_desktop_mime_type, sizes_desktop_filesize, sizes_desktop_filename 
      FROM media 
      ORDER BY created_at DESC 
      LIMIT 1;
    `);

    console.log('✅ Media query test successful');
    console.log(`📊 Records in media table: ${testQuery.rows.length}`);

    // Check S3 configuration
    const s3Config = {
      bucket: process.env.S3_BUCKET,
      accessKeyId: process.env.S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
      region: process.env.S3_REGION,
      endpoint: process.env.S3_ENDPOINT
    };

    const missingS3Config = Object.entries(s3Config)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    if (missingS3Config.length === 0) {
      console.log('✅ S3 configuration complete');
    } else {
      console.log('⚠️  Missing S3 config:', missingS3Config.join(', '));
    }

    console.log('\n🎉 Media System Status: READY');
    console.log('📝 Next steps:');
    console.log('   1. Start development server: pnpm dev');
    console.log('   2. Access admin: http://localhost:3000/admin');
    console.log('   3. Go to Media collection to upload files');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await client.end();
  }
}

verifyMediaSystem();
