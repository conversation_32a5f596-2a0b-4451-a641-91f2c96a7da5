import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ueiouusrrngdjrcoctem.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVlaW91dXNycm5nZGpyY29jdGVtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MzU2ODUsImV4cCI6MjA2NTExMTY4NX0.eeBn7QFGZUBOfVR5xeDaL_yo4cfVjWhgGDjBRbiYWCU';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkMediaSchema() {
  console.log('🔍 Checking media table schema...\n');

  try {
    // Get current schema
    const { data: columns, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_schema', 'public')
      .eq('table_name', 'media')
      .order('ordinal_position');

    if (error) {
      console.error('❌ Error checking schema:', error);
      return;
    }

    console.log('📋 Current media table schema:');
    columns.forEach(col => {
      console.log(`  ✓ ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(required)'}`);
    });

    // Expected columns from the error message
    const expectedColumns = [
      'id', 'alt', 'caption', 'category', 'prefix', 'updated_at', 'created_at', 
      'url', 'thumbnail_u_r_l', 'filename', 'mime_type', 'filesize', 'width', 'height', 
      'focal_x', 'focal_y', 'sizes_thumbnail_url', 'sizes_thumbnail_width', 
      'sizes_thumbnail_height', 'sizes_thumbnail_mime_type', 'sizes_thumbnail_filesize', 
      'sizes_thumbnail_filename', 'sizes_card_url', 'sizes_card_width', 'sizes_card_height', 
      'sizes_card_mime_type', 'sizes_card_filesize', 'sizes_card_filename', 
      'sizes_tablet_url', 'sizes_tablet_width', 'sizes_tablet_height', 
      'sizes_tablet_mime_type', 'sizes_tablet_filesize', 'sizes_tablet_filename', 
      'sizes_desktop_url', 'sizes_desktop_width', 'sizes_desktop_height', 
      'sizes_desktop_mime_type', 'sizes_desktop_filesize', 'sizes_desktop_filename'
    ];

    const existingColumns = columns.map(col => col.column_name);
    const missingColumns = expectedColumns.filter(col => !existingColumns.includes(col));

    if (missingColumns.length > 0) {
      console.log('\n❌ Missing columns:');
      missingColumns.forEach(col => console.log(`  - ${col}`));
    } else {
      console.log('\n✅ All expected columns are present!');
    }

    // Test a simple query
    console.log('\n🧪 Testing simple query...');
    const { data: testData, error: testError } = await supabase
      .from('media')
      .select('id, filename, mime_type')
      .limit(1);

    if (testError) {
      console.log('❌ Query error:', testError.message);
    } else {
      console.log('✅ Simple query works!');
      console.log('📊 Sample data:', testData);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkMediaSchema();
