async function testMediaEndpoint() {
  console.log('🧪 Testing media endpoint...\n');

  try {
    // Test the media API endpoint
    const response = await fetch('http://localhost:3004/api/media?limit=5');
    
    if (!response.ok) {
      console.log('❌ API Error:', response.status, response.statusText);
      const errorText = await response.text();
      console.log('Error details:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ Media API is working!');
    console.log(`📊 Found ${data.totalDocs} media files`);
    console.log(`📄 Showing ${data.docs.length} records`);
    
    if (data.docs.length > 0) {
      console.log('\n📋 Sample media records:');
      data.docs.forEach((doc, index) => {
        console.log(`  ${index + 1}. ${doc.filename || 'No filename'} (${doc.mimeType || 'Unknown type'})`);
      });
    }

  } catch (error) {
    console.error('❌ Error testing media endpoint:', error.message);
  }
}

testMediaEndpoint();
