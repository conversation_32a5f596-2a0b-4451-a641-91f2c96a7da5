import dotenv from 'dotenv'
import { createClient } from '@supabase/supabase-js'

dotenv.config()

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
)

async function testPayloadIntegration() {
  console.log('🧪 Testing Payload CMS Integration with Real Supabase Data\n')
  console.log('=' .repeat(60))
  
  const results = {
    readOperations: { passed: 0, failed: 0, tests: [] },
    dataIntegrity: { passed: 0, failed: 0, tests: [] },
    relationships: { passed: 0, failed: 0, tests: [] },
    performance: { passed: 0, failed: 0, tests: [] },
    security: { passed: 0, failed: 0, tests: [] }
  }
  
  // Test 1: Read Operations
  console.log('📖 PHASE 1: READ OPERATIONS')
  console.log('-'.repeat(30))
  
  const readTests = [
    { table: 'services', expected: 5 },
    { table: 'time_slots', expected: 8 },
    { table: 'appointments', expected: 0 },
    { table: 'doctors', expected: 0 },
    { table: 'user_profiles', expected: 0 }
  ]
  
  for (const test of readTests) {
    try {
      const { data, error, count } = await supabase
        .from(test.table)
        .select('*', { count: 'exact' })
      
      if (error) {
        console.log(`❌ ${test.table}: ${error.message}`)
        results.readOperations.failed++
        results.readOperations.tests.push({ table: test.table, status: 'failed', error: error.message })
      } else {
        const actualCount = count || 0
        console.log(`✅ ${test.table}: ${actualCount} records (expected: ${test.expected})`)
        results.readOperations.passed++
        results.readOperations.tests.push({ 
          table: test.table, 
          status: 'passed', 
          count: actualCount,
          expected: test.expected,
          match: actualCount === test.expected
        })
      }
    } catch (err) {
      console.log(`❌ ${test.table}: ${err.message}`)
      results.readOperations.failed++
    }
  }
  
  console.log('')
  
  // Test 2: Data Integrity & Structure
  console.log('🔍 PHASE 2: DATA INTEGRITY & STRUCTURE')
  console.log('-'.repeat(40))
  
  // Test services data quality
  try {
    const { data: services } = await supabase.from('services').select('*')
    
    if (services && services.length > 0) {
      const service = services[0]
      const requiredFields = ['id', 'name', 'base_price', 'duration_minutes']
      const missingFields = requiredFields.filter(field => !service.hasOwnProperty(field))
      
      if (missingFields.length === 0) {
        console.log('✅ Services: All required fields present')
        results.dataIntegrity.passed++
        results.dataIntegrity.tests.push({ test: 'services_structure', status: 'passed' })
      } else {
        console.log(`❌ Services: Missing fields: ${missingFields.join(', ')}`)
        results.dataIntegrity.failed++
        results.dataIntegrity.tests.push({ test: 'services_structure', status: 'failed', missing: missingFields })
      }
      
      // Test data types
      const priceValid = typeof service.base_price === 'number' && service.base_price > 0
      const durationValid = typeof service.duration_minutes === 'number' && service.duration_minutes > 0
      
      if (priceValid && durationValid) {
        console.log('✅ Services: Data types are valid')
        results.dataIntegrity.passed++
        results.dataIntegrity.tests.push({ test: 'services_data_types', status: 'passed' })
      } else {
        console.log('❌ Services: Invalid data types detected')
        results.dataIntegrity.failed++
        results.dataIntegrity.tests.push({ test: 'services_data_types', status: 'failed' })
      }
    }
  } catch (err) {
    console.log(`❌ Services integrity test failed: ${err.message}`)
    results.dataIntegrity.failed++
  }
  
  // Test time slots data quality
  try {
    const { data: timeSlots } = await supabase.from('time_slots').select('*')
    
    if (timeSlots && timeSlots.length > 0) {
      const slot = timeSlots[0]
      const requiredFields = ['id', 'doctor_id', 'date', 'start_time', 'end_time']
      const missingFields = requiredFields.filter(field => !slot.hasOwnProperty(field))
      
      if (missingFields.length === 0) {
        console.log('✅ Time Slots: All required fields present')
        results.dataIntegrity.passed++
        results.dataIntegrity.tests.push({ test: 'time_slots_structure', status: 'passed' })
      } else {
        console.log(`❌ Time Slots: Missing fields: ${missingFields.join(', ')}`)
        results.dataIntegrity.failed++
        results.dataIntegrity.tests.push({ test: 'time_slots_structure', status: 'failed', missing: missingFields })
      }
    }
  } catch (err) {
    console.log(`❌ Time slots integrity test failed: ${err.message}`)
    results.dataIntegrity.failed++
  }
  
  console.log('')
  
  // Test 3: Security (RLS Policies)
  console.log('🔒 PHASE 3: SECURITY & RLS POLICIES')
  console.log('-'.repeat(35))
  
  const securityTests = ['doctors', 'user_profiles', 'appointments', 'payments', 'invoices']
  
  for (const table of securityTests) {
    try {
      const { error } = await supabase
        .from(table)
        .insert({ test: 'security_check' })
      
      if (error && error.message.includes('row-level security policy')) {
        console.log(`✅ ${table}: RLS policy active (blocks unauthorized inserts)`)
        results.security.passed++
        results.security.tests.push({ table, status: 'passed', protection: 'RLS active' })
      } else if (error) {
        console.log(`⚠️ ${table}: Protected by other constraints: ${error.message.substring(0, 50)}...`)
        results.security.passed++
        results.security.tests.push({ table, status: 'passed', protection: 'Other constraints' })
      } else {
        console.log(`❌ ${table}: No security protection detected`)
        results.security.failed++
        results.security.tests.push({ table, status: 'failed', protection: 'None' })
      }
    } catch (err) {
      console.log(`⚠️ ${table}: Security test error: ${err.message}`)
    }
  }
  
  console.log('')
  
  // Test 4: Performance Benchmarks
  console.log('⚡ PHASE 4: PERFORMANCE BENCHMARKS')
  console.log('-'.repeat(35))
  
  // Single table queries
  const singleQueryStart = Date.now()
  const { data: servicesPerf } = await supabase.from('services').select('*')
  const singleQueryTime = Date.now() - singleQueryStart
  
  console.log(`✅ Single table query: ${singleQueryTime}ms (${servicesPerf?.length || 0} records)`)
  
  if (singleQueryTime < 100) {
    results.performance.passed++
    results.performance.tests.push({ test: 'single_query', status: 'passed', time: singleQueryTime })
  } else {
    results.performance.failed++
    results.performance.tests.push({ test: 'single_query', status: 'failed', time: singleQueryTime })
  }
  
  // Parallel queries
  const parallelStart = Date.now()
  const [services, timeSlots, appointments] = await Promise.all([
    supabase.from('services').select('*'),
    supabase.from('time_slots').select('*'),
    supabase.from('appointments').select('*')
  ])
  const parallelTime = Date.now() - parallelStart
  
  console.log(`✅ Parallel queries: ${parallelTime}ms (3 tables simultaneously)`)
  
  if (parallelTime < 300) {
    results.performance.passed++
    results.performance.tests.push({ test: 'parallel_queries', status: 'passed', time: parallelTime })
  } else {
    results.performance.failed++
    results.performance.tests.push({ test: 'parallel_queries', status: 'failed', time: parallelTime })
  }
  
  console.log('')
  
  // Test 5: Payload CMS Compatibility
  console.log('🎯 PHASE 5: PAYLOAD CMS COMPATIBILITY')
  console.log('-'.repeat(40))
  
  // Test field mapping compatibility
  const payloadCompatibilityTests = [
    { collection: 'services', payloadFields: ['id', 'name', 'base_price', 'duration_minutes', 'description'] },
    { collection: 'time_slots', payloadFields: ['id', 'doctor_id', 'date', 'start_time', 'end_time', 'is_available'] }
  ]
  
  for (const test of payloadCompatibilityTests) {
    try {
      const { data } = await supabase.from(test.collection).select('*').limit(1)
      
      if (data && data.length > 0) {
        const record = data[0]
        const availableFields = Object.keys(record)
        const missingFields = test.payloadFields.filter(field => !availableFields.includes(field))
        
        if (missingFields.length === 0) {
          console.log(`✅ ${test.collection}: All Payload fields available`)
          results.relationships.passed++
        } else {
          console.log(`⚠️ ${test.collection}: Missing Payload fields: ${missingFields.join(', ')}`)
          results.relationships.failed++
        }
      }
    } catch (err) {
      console.log(`❌ ${test.collection}: Compatibility test failed`)
      results.relationships.failed++
    }
  }
  
  console.log('')
  
  // Final Report
  console.log('📊 FINAL TEST REPORT')
  console.log('=' .repeat(60))
  
  const totalTests = Object.values(results).reduce((sum, category) => sum + category.passed + category.failed, 0)
  const totalPassed = Object.values(results).reduce((sum, category) => sum + category.passed, 0)
  const totalFailed = Object.values(results).reduce((sum, category) => sum + category.failed, 0)
  
  console.log(`📈 Overall Results: ${totalPassed}/${totalTests} tests passed (${Math.round(totalPassed/totalTests*100)}%)`)
  console.log('')
  
  Object.entries(results).forEach(([category, result]) => {
    const total = result.passed + result.failed
    const percentage = total > 0 ? Math.round(result.passed/total*100) : 0
    console.log(`${category.toUpperCase()}: ${result.passed}/${total} passed (${percentage}%)`)
  })
  
  console.log('')
  console.log('🎯 RECOMMENDATIONS:')
  
  if (results.security.passed > 0) {
    console.log('✅ Security: RLS policies are working - your data is protected')
  }
  
  if (results.performance.passed > 0) {
    console.log('✅ Performance: Query times are acceptable for production')
  }
  
  if (results.readOperations.passed === readTests.length) {
    console.log('✅ Data Access: All tables are accessible and ready for Payload integration')
  }
  
  console.log('')
  console.log('🚀 NEXT STEPS:')
  console.log('1. Apply performance indexes from docs/perf-indexes.sql')
  console.log('2. Configure RLS policies for Payload CMS service role')
  console.log('3. Test admin interface with existing data')
  console.log('4. Set up proper authentication flow')
  
  return results
}

testPayloadIntegration().catch(console.error)
