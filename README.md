# 🏥 Lullaby Clinic Management System

<p align="center">
  <img src="https://img.shields.io/badge/Payload%20CMS-3.0-blue?style=for-the-badge&logo=payloadcms" alt="Payload CMS 3.0" />
  <img src="https://img.shields.io/badge/Supabase-PostgreSQL-green?style=for-the-badge&logo=supabase" alt="Supabase" />
  <img src="https://img.shields.io/badge/Next.js-15-black?style=for-the-badge&logo=next.js" alt="Next.js 15" />
  <img src="https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript" alt="TypeScript" />
</p>

<p align="center">
  <strong>A comprehensive clinic management system built with Payload CMS and Supabase</strong>
</p>

## ✨ Features

### 🏥 **Healthcare Management**
- **Patient Management** - Complete patient profiles and medical history
- **Doctor Profiles** - Staff management with specializations and schedules
- **Appointment Booking** - Full scheduling system with time slot management
- **Service Catalog** - Medical services with pricing and descriptions
- **Review System** - Patient feedback with approval workflow

### 💰 **Financial Operations**
- **Payment Processing** - Multiple payment methods with Stripe integration
- **Invoice Generation** - Automated billing with PDF support
- **Financial Reporting** - Revenue tracking and analytics

### 📊 **Business Intelligence**
- **Analytics Dashboard** - KPI metrics and performance tracking
- **Marketing Campaigns** - Promotional content management
- **Inventory Management** - Medical supplies and equipment tracking

### 🎨 **Content Management**
- **Blog System** - Healthcare articles and news
- **CMS Pages** - Dynamic website content
- **Media Library** - Image/video management with automatic resizing
- **SEO Optimization** - Built-in SEO tools

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- pnpm (recommended) or npm
- Supabase account
- AWS S3 or Supabase Storage

### 1. Clone & Install
```bash
git clone <your-repo-url>
cd lullaby-clinic
pnpm install
```

### 2. Environment Setup
```bash
cp .env.example .env
```

Configure your `.env` file:
```env
# Database
DATABASE_URI=postgresql://user:pass@host:port/dbname
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key

# Storage
S3_ENDPOINT=https://your-project.supabase.co/storage/v1/s3
S3_BUCKET=your-bucket-name
S3_ACCESS_KEY_ID=your-access-key
S3_SECRET_ACCESS_KEY=your-secret-key

# App
PAYLOAD_SECRET=your-32-char-secret
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
```

### 3. Database Setup
```bash
# Run database migrations
pnpm migrate

# Verify database connection
pnpm test:supabase
```

### 4. Start Development
```bash
pnpm dev
```

Visit `http://localhost:3000/admin` to access the admin panel.

## 📋 Collections Overview

### 👥 **User Management**
- **Users** - System authentication
- **User Profiles** - Patient information
- **Admin Profiles** - Staff management

### 🏥 **Healthcare Operations**
- **Appointments** - Booking and scheduling
- **Doctors** - Medical staff profiles
- **Services** - Medical services catalog
- **Time Slots** - Availability management
- **Reviews** - Patient feedback system

### 💼 **Business Management**
- **Payments** - Transaction processing
- **Invoices** - Billing and receipts
- **Inventory Items** - Medical supplies
- **Clinic Settings** - System configuration

### 📈 **Analytics & Marketing**
- **Analytics Events** - User behavior tracking
- **KPI Metrics** - Performance indicators
- **Marketing Campaigns** - Promotional content
- **Promotions** - Special offers

### 📝 **Content Management**
- **Blog Posts** - Healthcare articles
- **CMS Pages** - Website content
- **Media** - File and image management

## 🛠️ Development Commands

```bash
# Development
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm start            # Start production server

# Database
pnpm migrate          # Run database migrations
pnpm test:supabase    # Test database connection
pnpm test:crud        # Test CRUD operations

# Media & Storage
pnpm test:media       # Test media upload system
pnpm test:storage     # Test S3 storage connection
pnpm verify:media     # Verify media system health

# Data Management
pnpm migrate:data     # Migrate legacy data
pnpm generate:schemas # Generate collection schemas
```

## 🏗️ Architecture

### **Tech Stack**
- **Frontend**: Next.js 15 with React 19
- **Backend**: Payload CMS 3.0
- **Database**: PostgreSQL via Supabase
- **Storage**: Supabase Storage (S3 compatible)
- **Authentication**: Payload Auth + Supabase RLS
- **Deployment**: Vercel/Railway ready

### **Key Integrations**
- **Supabase Adapter** - Custom database adapter for Payload CMS
- **S3 Storage Handler** - Media upload with automatic resizing
- **TypeScript Types** - Fully typed collections and API
- **Security** - Row Level Security (RLS) policies

## 📁 Project Structure

```
lullaby-clinic/
├── src/
│   ├── collections/          # Payload CMS collections
│   ├── lib/                  # Utilities and adapters
│   ├── app/                  # Next.js app directory
│   └── migrations/           # Database migrations
├── scripts/                  # Development and testing scripts
├── docs/                     # Documentation and SQL files
└── _schema/                  # Generated database schemas
```

## 🔒 Security Features

- **Row Level Security (RLS)** - Database-level access control
- **Authentication** - Secure user management
- **API Protection** - Rate limiting and validation
- **File Upload Security** - Virus scanning and type validation
- **Environment Variables** - Secure configuration management

## 📊 Performance

- **Database Indexing** - Optimized queries (see `docs/perf-indexes.sql`)
- **Image Optimization** - Automatic resizing and compression
- **Caching** - Built-in Next.js caching
- **CDN Ready** - Optimized for global delivery

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Railway
```bash
# Connect to Railway
railway login
railway link

# Deploy
railway up
```

### Docker
```bash
# Build image
docker build -t lullaby-clinic .

# Run container
docker run -p 3000:3000 lullaby-clinic
```

## 🧪 Testing

The system includes comprehensive testing scripts:

- **Database Tests** - Connection and schema validation
- **CRUD Tests** - Create, read, update, delete operations
- **Media Tests** - File upload and processing
- **Integration Tests** - End-to-end functionality
- **Performance Tests** - Query optimization validation

## 📚 Documentation

- **[CLAUDE.md](./CLAUDE.md)** - Detailed development guide
- **[STATUS.md](./STATUS.md)** - Project status and progress
- **[Performance Indexes](./docs/perf-indexes.sql)** - Database optimization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check [CLAUDE.md](./CLAUDE.md) for detailed guides
- **Issues**: Open a GitHub issue for bugs or feature requests
- **Payload CMS**: [Official Documentation](https://payloadcms.com/docs)
- **Supabase**: [Official Documentation](https://supabase.com/docs)

---

<p align="center">
  <strong>Built with ❤️ for healthcare professionals</strong>
</p>
