# Lullaby Clinic Backend - Claude AI Integration Notes

This document contains comprehensive notes and guidelines for working with <PERSON> on the Lullaby Clinic Payload CMS backend project.

## Project Overview
This is the backend API and content management system for the Lullaby Clinic project, built with Payload CMS 3.0. It serves as the headless CMS and API layer that powers the React frontend application, providing content management, user authentication, media handling, and data persistence for the clinic management system.

## Key Technologies
- **CMS Framework**: Payload CMS 3.0 with Next.js 15.3.0
- **Database**: PostgreSQL with @payloadcms/db-postgres adapter
- **Rich Text Editor**: Lexical (@payloadcms/richtext-lexical)
- **Runtime**: Node.js with ES Modules
- **Image Processing**: Sharp for media optimization
- **Cloud Integration**: Payload Cloud plugin for deployment
- **API**: Auto-generated GraphQL and REST APIs
- **Authentication**: Built-in Payload authentication system
- **TypeScript**: Full TypeScript support with auto-generated types

## Architecture Overview

### System Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Lullaby Clinic System                    │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React + Supabase)                               │
│  Location: /lullaby-homepage-checks                        │
│  ├── User Interface (<PERSON>ients, Staff)                      │
│  ├── Admin Dashboard                                        │
│  ├── Appointment Management                                 │
│  └── Authentication (Supabase)                             │
├─────────────────────────────────────────────────────────────┤
│  Backend (Payload CMS) - THIS PROJECT                      │
│  Location: /lullaby-backend-payload/lullaby-clinic         │
│  ├── Content Management System                             │
│  ├── Media Management                                       │
│  ├── User Management                                        │
│  ├── GraphQL/REST APIs                                     │
│  └── PostgreSQL Database                                   │
└─────────────────────────────────────────────────────────────┘
```

### Payload CMS Architecture
- **Headless CMS**: Provides admin interface and API endpoints
- **Collection-Based**: Data organized into collections (Users, Media)
- **Auto-Generated APIs**: GraphQL and REST endpoints for all collections
- **Admin Panel**: Web-based content management interface
- **Role-Based Access**: Built-in authentication and authorization
- **Media Management**: File upload and processing capabilities

### File Organization
```
src/
├── collections/
│   ├── Users.ts            # User collection definition
│   └── Media.ts            # Media collection definition
├── payload.config.ts       # Main Payload configuration
├── payload-types.ts        # Auto-generated TypeScript types
└── app/                    # Next.js app directory
```

## Collections & Data Models

### Current Collections

#### Users Collection
- **Purpose**: Manages user accounts with authentication
- **Features**: Email/password authentication, role-based access
- **Admin Access**: Users can access the Payload admin panel
- **Integration**: Serves as authentication layer for the system

#### Media Collection
- **Purpose**: Handles file uploads and media management
- **Features**: Image optimization, multiple sizes, focal point selection
- **Storage**: Configurable storage adapters (local, cloud)
- **Processing**: Sharp integration for image manipulation

### Timezone Support
Recent additions include comprehensive timezone support with IANA timezone identifiers:

```typescript
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'America/Los_Angeles'
  | 'America/Denver'
  | 'America/Chicago'
  | 'America/New_York'
  // ... and many more
```

This suggests upcoming appointment scheduling or time-sensitive features for the clinic management system.

## Development Guidelines

### Environment Setup
```bash
# Required environment variables
PAYLOAD_SECRET=your-secret-key-here
DATABASE_URI=postgresql://postgres:[YOUR-PASSWORD]@db.ueiouusrrngdjrcoctem.supabase.co:5432/postgres
NODE_OPTIONS=--no-deprecation

# Optional cloud deployment variables
PAYLOAD_CLOUD_PROJECT_ID=your-project-id
PAYLOAD_CLOUD_API_KEY=your-api-key
```

### Development Scripts
```bash
# install dependencies
pnpm install

# Development server with hot reload
pnpm dev

# Safe development (clears Next.js cache)
pnpm run devsafe

# Production build
pnpm run build

# Start production server
pnpm run start

# Generate TypeScript types
pnpm run generate:types

# Generate import map
pnpm run generate:importmap

# Run linting
pnpm run lint

```

### Code Quality Standards
- **TypeScript Strict Mode**: All code must pass TypeScript checks
- **Auto-Generated Types**: Use generated types from payload-types.ts
- **Collection Patterns**: Follow established collection structure
- **Error Handling**: Implement proper error handling in hooks and operations
- **Environment Variables**: Never hardcode secrets or configuration

### Performance Considerations
- **Sharp Integration**: Optimized image processing
- **Database Indexing**: Proper PostgreSQL indexing for queries
- **Caching**: Leverage Payload's built-in caching mechanisms
- **Bundle Optimization**: Tree shaking and code splitting via Next.js

## API Documentation

### Auto-Generated APIs
Payload automatically generates both GraphQL and REST APIs for all collections:

#### GraphQL Endpoint
```
POST /api/graphql
```

#### REST Endpoints
```
# Users Collection
GET    /api/users          # List users
POST   /api/users          # Create user
GET    /api/users/:id      # Get user by ID
PATCH  /api/users/:id      # Update user
DELETE /api/users/:id      # Delete user

# Media Collection
GET    /api/media          # List media
POST   /api/media          # Upload media
GET    /api/media/:id      # Get media by ID
PATCH  /api/media/:id      # Update media
DELETE /api/media/:id      # Delete media
```

#### Authentication Endpoints
```
POST   /api/users/login           # User login
POST   /api/users/logout          # User logout
POST   /api/users/refresh-token   # Refresh JWT token
POST   /api/users/forgot-password # Password reset
GET    /api/users/me             # Current user info
```

### API Usage Patterns
```typescript
// GraphQL Query Example
const GET_USERS = `
  query GetUsers {
    Users {
      docs {
        id
        email
        createdAt
        updatedAt
      }
    }
  }
`;

// REST API Example
const response = await fetch('/api/users', {
  headers: {
    'Authorization': `JWT ${token}`,
    'Content-Type': 'application/json'
  }
});
```

## Authentication & Security

### Built-in Authentication
- **JWT Tokens**: Secure token-based authentication
- **Password Hashing**: Bcrypt password hashing
- **Session Management**: Automatic session handling
- **CSRF Protection**: Built-in CSRF protection
- **Rate Limiting**: Configurable rate limiting

### Role-Based Access Control
```typescript
// Collection-level access control
access: {
  read: ({ req: { user } }) => {
    // Custom access logic
    return Boolean(user);
  },
  create: ({ req: { user } }) => {
    // Only authenticated users can create
    return Boolean(user);
  },
  update: ({ req: { user } }) => {
    // Users can only update their own records
    return user?.id;
  },
  delete: ({ req: { user } }) => {
    // Admin-only delete access
    return user?.role === 'admin';
  }
}
```

### Security Best Practices
- **Environment Variables**: Store secrets in environment variables
- **HTTPS Only**: Enforce HTTPS in production
- **Input Validation**: Validate all inputs through Payload's validation system
- **SQL Injection Prevention**: Use Payload's query builder (no raw SQL)
- **File Upload Security**: Validate file types and sizes

## Integration with Frontend

### Frontend Connection
The React frontend (located at `/lullaby-homepage-checks`) integrates with this Payload backend through:

#### API Integration
- **REST API Calls**: Frontend makes HTTP requests to Payload endpoints
- **GraphQL Queries**: Optional GraphQL integration for complex queries
- **Authentication**: JWT token management between systems
- **Media URLs**: Direct media file serving from Payload

#### Data Flow
```
React Frontend (Supabase Auth) ←→ Payload CMS Backend (Content & Media)
                ↓                              ↓
        User Authentication              Content Management
        Appointment Data                 Media Storage
        Dashboard Analytics             User Profiles
```

#### Dual Authentication Strategy
- **Frontend**: Supabase authentication for user sessions
- **Backend**: Payload authentication for admin/content management
- **Integration**: Potential user synchronization between systems

## Docker & Deployment

### Docker Configuration
```dockerfile
# Dockerfile example
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

### Docker Compose Setup
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: lullaby_clinic
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  payload:
    build: .
    environment:
      DATABASE_URI: ********************************************/lullaby_clinic
      PAYLOAD_SECRET: your-secret-here
    ports:
      - "3000:3000"
    depends_on:
      - postgres

volumes:
  postgres_data:
```

### Cloud Deployment
- **Payload Cloud**: Native cloud hosting with automatic scaling
- **Vercel**: Next.js optimized deployment
- **Railway**: Simple PostgreSQL + Node.js hosting
- **AWS/GCP/Azure**: Container-based deployment options

### Environment Configuration
```bash
# Production environment variables
NODE_ENV=production
DATABASE_URI=postgresql://user:pass@host:port/db
PAYLOAD_SECRET=secure-random-string
PAYLOAD_CLOUD_PROJECT_ID=project-id
```

## AI Assistance Patterns

### Collection Development
When working with Claude on collection development:

```typescript
// Example prompt pattern:
"Create a new Payload collection for Appointments with the following fields:
- appointmentDate (date field)
- patientName (text field, required)
- doctorId (relationship to Users collection)
- status (select field with options: scheduled, completed, cancelled)
- notes (rich text field using Lexical editor)
- timezone (select field using SupportedTimezones type)"
```

### Type Generation Workflow
```bash
# After making collection changes:
1. npm run generate:types
2. npm run generate:importmap
3. npm run dev (to test changes)
4. Commit the updated payload-types.ts file
```

### Common AI Prompts
```markdown
# Collection Creation
"Add a new collection called 'Patients' with fields for personal information, medical history, and relationship to appointments"

# Field Modification
"Add a timezone field to the existing Media collection using the SupportedTimezones type"

# Access Control
"Implement role-based access control where only admin users can delete records from the Users collection"

# API Integration
"Show me how to query the Users collection from the React frontend using the REST API"

# Validation Rules
"Add validation to ensure email fields are unique and properly formatted in the Users collection"
```

### Code Modification Guidelines
- **Preserve Existing Logic**: Don't modify working authentication or access control
- **Follow Payload Patterns**: Use established Payload collection patterns
- **Type Safety**: Ensure all changes maintain TypeScript compatibility
- **Test Locally**: Always test changes with `npm run dev`
- **Regenerate Types**: Run type generation after schema changes

## Troubleshooting Guide

### Common Issues

#### Database Connection Problems
```bash
# Check database connection
npm run payload -- --help

# Verify environment variables
echo $DATABASE_URI

# Test PostgreSQL connection
psql $DATABASE_URI -c "SELECT version();"
```

#### Type Generation Issues
```bash
# Clear Next.js cache and regenerate
rm -rf .next
npm run generate:types
npm run generate:importmap
npm run dev
```

#### Authentication Problems
```bash
# Check JWT secret configuration
echo $PAYLOAD_SECRET

# Verify user creation in admin panel
# Navigate to http://localhost:3000/admin
```

#### Build Errors
```bash
# Check TypeScript compilation
npx tsc --noEmit

# Verify all dependencies
npm ci

# Check for missing environment variables
npm run build
```

### Debug Steps
1. **Check Console Logs**: Review server console for error messages
2. **Verify Environment**: Ensure all required environment variables are set
3. **Database Status**: Confirm PostgreSQL connection and table creation
4. **Admin Panel**: Use `/admin` route to verify collection setup
5. **API Testing**: Test endpoints directly with curl or Postman

### Performance Issues
```bash
# Database query optimization
# Check slow queries in PostgreSQL logs

# Bundle size analysis
npm run build
# Review build output for large bundles

# Memory usage monitoring
# Use Node.js --inspect flag for debugging
```

## Migration & Updates

### Database Migrations
Payload handles database schema automatically, but for custom migrations:

```typescript
// Custom migration example
export const migration = {
  up: async ({ payload }) => {
    // Migration logic here
    await payload.db.drizzle.execute(sql`
      ALTER TABLE users ADD COLUMN timezone VARCHAR(50);
    `);
  },
  down: async ({ payload }) => {
    // Rollback logic here
    await payload.db.drizzle.execute(sql`
      ALTER TABLE users DROP COLUMN timezone;
    `);
  }
};
```

### Version Updates
```bash
# Update Payload CMS
npm update payload @payloadcms/next @payloadcms/db-postgres

# Update Next.js
npm update next

# Update all dependencies
npm update

# Check for breaking changes
npm run build
npm run generate:types
```

### Data Backup Strategy
```bash
# PostgreSQL backup
pg_dump $DATABASE_URI > backup.sql

# Media files backup
# Backup uploaded files directory or cloud storage

# Configuration backup
# Commit all configuration files to version control
```

## Monitoring & Analytics

### Health Monitoring
```typescript
// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  });
});
```

### Performance Metrics
- **Database Query Performance**: Monitor slow queries
- **API Response Times**: Track endpoint performance
- **Memory Usage**: Monitor Node.js memory consumption
- **Error Rates**: Track API error responses

### Logging Strategy
```typescript
// Structured logging
payload.logger.info('User created', {
  userId: user.id,
  email: user.email,
  timestamp: new Date().toISOString()
});
```

## Future Enhancements

### Planned Features
Based on the timezone support addition, upcoming features may include:
- **Appointment Scheduling**: Time-zone aware appointment management
- **Calendar Integration**: Integration with external calendar systems
- **Notification System**: Email/SMS notifications for appointments
- **Reporting Dashboard**: Analytics and reporting capabilities
- **Multi-location Support**: Support for multiple clinic locations

### Integration Opportunities
- **Frontend Synchronization**: Better integration with React frontend
- **Third-party APIs**: Integration with medical record systems
- **Payment Processing**: Integration with payment gateways
- **Telemedicine**: Video consultation capabilities

## Notes for AI Assistance

### Before Making Changes
- **Review Current Schema**: Check existing collections and fields
- **Understand Relationships**: Verify collection relationships and dependencies
- **Check Access Control**: Understand current permission structure
- **Validate Environment**: Ensure proper development environment setup

### Code Modification Best Practices
- **Incremental Changes**: Make small, testable changes
- **Type Safety**: Maintain TypeScript compatibility
- **Documentation**: Update relevant documentation
- **Testing**: Test changes thoroughly in development

### Communication Guidelines
- **Clear Requirements**: Provide specific, detailed requirements
- **Context Awareness**: Understand the clinic management domain
- **Integration Impact**: Consider effects on frontend integration
- **Performance Implications**: Evaluate performance impact of changes

### Common Patterns
```typescript
// Collection definition pattern
export const CollectionName: CollectionConfig = {
  slug: 'collection-name',
  admin: {
    useAsTitle: 'title',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => Boolean(user),
    update: ({ req: { user } }) => Boolean(user),
    delete: ({ req: { user } }) => Boolean(user),
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    // Additional fields...
  ],
};
```

---

**Last Updated**: December 2024
**Project Version**: 1.0
**Payload CMS Version**: 3.43.0
**AI Integration**: Claude AI Compatible
**Frontend Integration**: React + Supabase (/lullaby-homepage-checks)

cd /Users/<USER>/Documents/project/lullabyclinic/lullaby-backend-payload/lullaby-clinic && PGPASSWORD="Resworpp@191" psql -h aws-0-ap-southeast-1.pooler.supabase.com -p 6543 -U postgres.ueiouusrrngdjrcoctem -d postgres -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;"
Result: table_name
-------------------------------
 admin_activity_logs
 admin_profiles
 admin_users
 analytics_events
 appointment_slots
 appointment_types
 appointments
 blog_categories
 blog_posts
 blog_tags
 campaign_recipients
 clinic_settings
 cms_media
 cms_page_categories
 cms_page_tags
 cms_pages
 customer_interactions
 customer_notes
 customer_segments
 doctor_services
 doctors
 email_templates
 inventory_items
 inventory_transactions
 invoices
 kpi_metrics
 marketing_campaigns
 media
 newsletter_subscribers
 notifications
 payload_locked_documents
 payload_locked_documents_rels
 payload_migrations
 payload_preferences
 payload_preferences_rels
 payments
 promotion_usage
 promotions
 reviews
 segment_memberships
 service_categories
 services
 time_slots
 user_profiles
 users
 website_metrics
(46 rows)

Friday:
Wow! 🎉 Your Supabase database already has 46 tables! This is much more comprehensive than what the migration files showed. You have a
full-featured lullaby clinic system already set up with:


                                                        🏥 Healthcare & Clinic Management:

 • appointments & appointment_slots & appointment_types
 • doctors & doctor_services
 • services & service_categories
 • time_slots
 • clinic_settings


                                                          👥 User & Customer Management:

 • users & user_profiles
 • admin_users & admin_profiles
 • customer_interactions & customer_notes
 • customer_segments & segment_memberships


                                                              💰 Business & Finance:

 • invoices & payments
 • promotions & promotion_usage
 • inventory_items & inventory_transactions


                                                             📝 Content & Marketing:

 • blog_posts & blog_categories & blog_tags
 • cms_pages & cms_page_categories & cms_page_tags
 • marketing_campaigns & campaign_recipients
 • newsletter_subscribers
 • email_templates


                                                             📊 Analytics & Reviews:

 • analytics_events & website_metrics
 • kpi_metrics
 • reviews
 • notifications
