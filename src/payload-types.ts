/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    appointments: Appointment;
    doctors: Doctor;
    services: Service;
    'time-slots': TimeSlot;
    'clinic-settings': ClinicSetting;
    'user-profiles': UserProfile;
    'admin-profiles': AdminProfile;
    invoices: Invoice;
    payments: Payment;
    promotions: Promotion;
    'inventory-items': InventoryItem;
    'blog-posts': BlogPost;
    'cms-pages': CmsPage;
    'marketing-campaigns': MarketingCampaign;
    reviews: Review;
    'analytics-events': AnalyticsEvent;
    'kpi-metrics': KpiMetric;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    appointments: AppointmentsSelect<false> | AppointmentsSelect<true>;
    doctors: DoctorsSelect<false> | DoctorsSelect<true>;
    services: ServicesSelect<false> | ServicesSelect<true>;
    'time-slots': TimeSlotsSelect<false> | TimeSlotsSelect<true>;
    'clinic-settings': ClinicSettingsSelect<false> | ClinicSettingsSelect<true>;
    'user-profiles': UserProfilesSelect<false> | UserProfilesSelect<true>;
    'admin-profiles': AdminProfilesSelect<false> | AdminProfilesSelect<true>;
    invoices: InvoicesSelect<false> | InvoicesSelect<true>;
    payments: PaymentsSelect<false> | PaymentsSelect<true>;
    promotions: PromotionsSelect<false> | PromotionsSelect<true>;
    'inventory-items': InventoryItemsSelect<false> | InventoryItemsSelect<true>;
    'blog-posts': BlogPostsSelect<false> | BlogPostsSelect<true>;
    'cms-pages': CmsPagesSelect<false> | CmsPagesSelect<true>;
    'marketing-campaigns': MarketingCampaignsSelect<false> | MarketingCampaignsSelect<true>;
    reviews: ReviewsSelect<false> | ReviewsSelect<true>;
    'analytics-events': AnalyticsEventsSelect<false> | AnalyticsEventsSelect<true>;
    'kpi-metrics': KpiMetricsSelect<false> | KpiMetricsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * Upload and manage images, videos, and documents
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  /**
   * Alternative text for accessibility
   */
  alt: string;
  /**
   * Optional caption for the media
   */
  caption?: string | null;
  /**
   * Categorize media for better organization
   */
  category?: ('profile' | 'service' | 'clinic' | 'blog' | 'marketing' | 'documents' | 'other') | null;
  prefix?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    tablet?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    desktop?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "appointments".
 */
export interface Appointment {
  id: number;
  patient: number | UserProfile;
  doctor: number | Doctor;
  service?: (number | null) | Service;
  timeSlot?: (number | null) | TimeSlot;
  appointment_date: string;
  duration_minutes?: number | null;
  status?: ('scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show') | null;
  total_amount: number;
  deposit_amount?: number | null;
  patient_notes?: string | null;
  doctor_notes?: string | null;
  treatment_plan?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  before_photos?: (number | Media)[] | null;
  after_photos?: (number | Media)[] | null;
  prescription?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  next_appointment_recommended?: boolean | null;
  followup_date?: string | null;
  confirmation_sent_at?: string | null;
  reminder_sent_at?: string | null;
  checked_in_at?: string | null;
  completed_at?: string | null;
  cancelled_at?: string | null;
  cancellation_reason?: string | null;
  patient_phone?: string | null;
  patient_email?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-profiles".
 */
export interface UserProfile {
  id: number;
  role?: string | null;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string | null;
  date_of_birth?: string | null;
  gender?: string | null;
  address?: string | null;
  city?: string | null;
  country?: string | null;
  emergency_contact_name?: string | null;
  emergency_contact_phone?: string | null;
  medical_history?: string | null;
  allergies?: string | null;
  current_medications?: string | null;
  preferred_language?: string | null;
  marketing_consent?: boolean | null;
  privacy_consent?: boolean | null;
  last_login?: string | null;
  is_active?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "doctors".
 */
export interface Doctor {
  id: number;
  license_number: string;
  specialization: string;
  qualification: string;
  experience_years?: number | null;
  bio?: string | null;
  consultation_fee?: number | null;
  languages_spoken?: string | null;
  working_hours?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  is_available?: boolean | null;
  rating?: number | null;
  total_reviews?: number | null;
  image_url?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "services".
 */
export interface Service {
  id: number;
  category_id?: string | null;
  name: string;
  description: string;
  short_description?: string | null;
  slug: string;
  base_price: number;
  discounted_price?: number | null;
  duration_minutes?: number | null;
  difficulty?: string | null;
  preparation_instructions?: string | null;
  aftercare_instructions?: string | null;
  contraindications?: string | null;
  benefits?: string | null;
  procedures?: string | null;
  image_url?: string | null;
  gallery_images?: string | null;
  is_popular?: boolean | null;
  is_featured?: boolean | null;
  sort_order?: number | null;
  is_active?: boolean | null;
  seo_title?: string | null;
  seo_description?: string | null;
  seo_keywords?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "time-slots".
 */
export interface TimeSlot {
  id: number;
  doctor_id?: string | null;
  date: string;
  start_time: string;
  end_time: string;
  is_available?: boolean | null;
  is_recurring?: boolean | null;
  recurring_pattern?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "clinic-settings".
 */
export interface ClinicSetting {
  id: number;
  key: string;
  value?: string | null;
  description?: string | null;
  data_type?: string | null;
  is_public?: boolean | null;
  updated_by?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "admin-profiles".
 */
export interface AdminProfile {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string | null;
  avatar_url?: string | null;
  bio?: string | null;
  preferences?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "invoices".
 */
export interface Invoice {
  id: number;
  appointment?: (number | null) | Appointment;
  payment?: (number | null) | Payment;
  invoice_number: string;
  issue_date?: string | null;
  due_date?: string | null;
  subtotal: number;
  tax_amount?: number | null;
  discount_amount?: number | null;
  total_amount: number;
  currency?: ('USD' | 'EUR' | 'GBP' | 'THB') | null;
  notes?: string | null;
  is_paid?: boolean | null;
  pdf_url?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payments".
 */
export interface Payment {
  id: number;
  appointment?: (number | null) | Appointment;
  patient?: (number | null) | UserProfile;
  amount: number;
  payment_method: 'credit_card' | 'debit_card' | 'cash' | 'bank_transfer' | 'digital_wallet' | 'insurance';
  payment_status?:
    | ('pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded' | 'partially_refunded')
    | null;
  transaction_id?: string | null;
  stripe_payment_intent_id?: string | null;
  payment_date?: string | null;
  refund_amount?: number | null;
  refund_date?: string | null;
  refund_reason?: string | null;
  receipt_url?: string | null;
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "promotions".
 */
export interface Promotion {
  id: number;
  title: string;
  description: string;
  discount_type: string;
  discount_value: number;
  min_purchase_amount?: number | null;
  max_discount_amount?: number | null;
  promo_code?: string | null;
  start_date: string;
  end_date: string;
  usage_limit?: number | null;
  usage_count?: number | null;
  applicable_services?: string | null;
  is_active?: boolean | null;
  is_featured?: boolean | null;
  image_url?: string | null;
  terms_conditions?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "inventory-items".
 */
export interface InventoryItem {
  id: number;
  name: string;
  description?: string | null;
  category: string;
  sku?: string | null;
  barcode?: string | null;
  unit_of_measure: string;
  unit_cost?: number | null;
  current_stock?: number | null;
  minimum_stock?: number | null;
  maximum_stock?: number | null;
  reorder_point?: number | null;
  supplier?: string | null;
  supplier_contact?: string | null;
  storage_location?: string | null;
  expiry_tracking?: boolean | null;
  is_active?: boolean | null;
  created_by?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-posts".
 */
export interface BlogPost {
  id: number;
  author_id?: string | null;
  title: string;
  slug: string;
  excerpt?: string | null;
  content: string;
  featured_image?: string | null;
  gallery_images?: string | null;
  category?: string | null;
  tags?: string | null;
  difficulty?: string | null;
  read_time_minutes?: number | null;
  view_count?: number | null;
  like_count?: number | null;
  is_published?: boolean | null;
  is_featured?: boolean | null;
  published_at?: string | null;
  seo_title?: string | null;
  seo_description?: string | null;
  seo_keywords?: string | null;
  language?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "cms-pages".
 */
export interface CmsPage {
  id: number;
  slug: string;
  title:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  content:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  excerpt?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  meta_title?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  meta_description?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  meta_keywords?: string | null;
  featured_image_url?: string | null;
  is_published?: boolean | null;
  publish_at?: string | null;
  page_type?: string | null;
  template?: string | null;
  sort_order?: number | null;
  view_count?: number | null;
  created_by?: string | null;
  updated_by?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "marketing-campaigns".
 */
export interface MarketingCampaign {
  id: number;
  name: string;
  description?: string | null;
  campaign_type: string;
  target_segment?: string | null;
  email_template_id?: string | null;
  subject_line?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  content:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  sender_name?: string | null;
  sender_email?: string | null;
  scheduled_at?: string | null;
  started_at?: string | null;
  completed_at?: string | null;
  status?: string | null;
  total_recipients?: number | null;
  sent_count?: number | null;
  delivered_count?: number | null;
  opened_count?: number | null;
  clicked_count?: number | null;
  unsubscribed_count?: number | null;
  bounced_count?: number | null;
  budget_amount?: number | null;
  actual_cost?: number | null;
  roi_percentage?: number | null;
  created_by?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "reviews".
 */
export interface Review {
  id: number;
  patient?: (number | null) | UserProfile;
  doctor?: (number | null) | Doctor;
  service?: (number | null) | Service;
  appointment?: (number | null) | Appointment;
  rating: number;
  title?: string | null;
  comment?: string | null;
  is_anonymous?: boolean | null;
  is_approved?: boolean | null;
  is_featured?: boolean | null;
  helpful_count?: number | null;
  before_photo?: (number | null) | Media;
  after_photo?: (number | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "analytics-events".
 */
export interface AnalyticsEvent {
  id: number;
  event_type: string;
  event_category: string;
  event_data:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  patient_id?: string | null;
  session_id?: string | null;
  page_url?: string | null;
  referrer_url?: string | null;
  ip_address?: string | null;
  user_agent?: string | null;
  device_type?: string | null;
  browser?: string | null;
  os?: string | null;
  country?: string | null;
  city?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "kpi-metrics".
 */
export interface KpiMetric {
  id: number;
  date: string;
  metric_name: string;
  metric_value: number;
  metric_unit?: string | null;
  category: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'appointments';
        value: number | Appointment;
      } | null)
    | ({
        relationTo: 'doctors';
        value: number | Doctor;
      } | null)
    | ({
        relationTo: 'services';
        value: number | Service;
      } | null)
    | ({
        relationTo: 'time-slots';
        value: number | TimeSlot;
      } | null)
    | ({
        relationTo: 'clinic-settings';
        value: number | ClinicSetting;
      } | null)
    | ({
        relationTo: 'user-profiles';
        value: number | UserProfile;
      } | null)
    | ({
        relationTo: 'admin-profiles';
        value: number | AdminProfile;
      } | null)
    | ({
        relationTo: 'invoices';
        value: number | Invoice;
      } | null)
    | ({
        relationTo: 'payments';
        value: number | Payment;
      } | null)
    | ({
        relationTo: 'promotions';
        value: number | Promotion;
      } | null)
    | ({
        relationTo: 'inventory-items';
        value: number | InventoryItem;
      } | null)
    | ({
        relationTo: 'blog-posts';
        value: number | BlogPost;
      } | null)
    | ({
        relationTo: 'cms-pages';
        value: number | CmsPage;
      } | null)
    | ({
        relationTo: 'marketing-campaigns';
        value: number | MarketingCampaign;
      } | null)
    | ({
        relationTo: 'reviews';
        value: number | Review;
      } | null)
    | ({
        relationTo: 'analytics-events';
        value: number | AnalyticsEvent;
      } | null)
    | ({
        relationTo: 'kpi-metrics';
        value: number | KpiMetric;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  category?: T;
  prefix?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        card?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        tablet?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        desktop?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "appointments_select".
 */
export interface AppointmentsSelect<T extends boolean = true> {
  patient?: T;
  doctor?: T;
  service?: T;
  timeSlot?: T;
  appointment_date?: T;
  duration_minutes?: T;
  status?: T;
  total_amount?: T;
  deposit_amount?: T;
  patient_notes?: T;
  doctor_notes?: T;
  treatment_plan?: T;
  before_photos?: T;
  after_photos?: T;
  prescription?: T;
  next_appointment_recommended?: T;
  followup_date?: T;
  confirmation_sent_at?: T;
  reminder_sent_at?: T;
  checked_in_at?: T;
  completed_at?: T;
  cancelled_at?: T;
  cancellation_reason?: T;
  patient_phone?: T;
  patient_email?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "doctors_select".
 */
export interface DoctorsSelect<T extends boolean = true> {
  id?: T;
  license_number?: T;
  specialization?: T;
  qualification?: T;
  experience_years?: T;
  bio?: T;
  consultation_fee?: T;
  languages_spoken?: T;
  working_hours?: T;
  is_available?: T;
  rating?: T;
  total_reviews?: T;
  image_url?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "services_select".
 */
export interface ServicesSelect<T extends boolean = true> {
  id?: T;
  category_id?: T;
  name?: T;
  description?: T;
  short_description?: T;
  slug?: T;
  base_price?: T;
  discounted_price?: T;
  duration_minutes?: T;
  difficulty?: T;
  preparation_instructions?: T;
  aftercare_instructions?: T;
  contraindications?: T;
  benefits?: T;
  procedures?: T;
  image_url?: T;
  gallery_images?: T;
  is_popular?: T;
  is_featured?: T;
  sort_order?: T;
  is_active?: T;
  seo_title?: T;
  seo_description?: T;
  seo_keywords?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "time-slots_select".
 */
export interface TimeSlotsSelect<T extends boolean = true> {
  id?: T;
  doctor_id?: T;
  date?: T;
  start_time?: T;
  end_time?: T;
  is_available?: T;
  is_recurring?: T;
  recurring_pattern?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "clinic-settings_select".
 */
export interface ClinicSettingsSelect<T extends boolean = true> {
  id?: T;
  key?: T;
  value?: T;
  description?: T;
  data_type?: T;
  is_public?: T;
  updated_by?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-profiles_select".
 */
export interface UserProfilesSelect<T extends boolean = true> {
  id?: T;
  role?: T;
  first_name?: T;
  last_name?: T;
  email?: T;
  phone?: T;
  date_of_birth?: T;
  gender?: T;
  address?: T;
  city?: T;
  country?: T;
  emergency_contact_name?: T;
  emergency_contact_phone?: T;
  medical_history?: T;
  allergies?: T;
  current_medications?: T;
  preferred_language?: T;
  marketing_consent?: T;
  privacy_consent?: T;
  last_login?: T;
  is_active?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "admin-profiles_select".
 */
export interface AdminProfilesSelect<T extends boolean = true> {
  id?: T;
  first_name?: T;
  last_name?: T;
  email?: T;
  phone?: T;
  avatar_url?: T;
  bio?: T;
  preferences?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "invoices_select".
 */
export interface InvoicesSelect<T extends boolean = true> {
  appointment?: T;
  payment?: T;
  invoice_number?: T;
  issue_date?: T;
  due_date?: T;
  subtotal?: T;
  tax_amount?: T;
  discount_amount?: T;
  total_amount?: T;
  currency?: T;
  notes?: T;
  is_paid?: T;
  pdf_url?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payments_select".
 */
export interface PaymentsSelect<T extends boolean = true> {
  appointment?: T;
  patient?: T;
  amount?: T;
  payment_method?: T;
  payment_status?: T;
  transaction_id?: T;
  stripe_payment_intent_id?: T;
  payment_date?: T;
  refund_amount?: T;
  refund_date?: T;
  refund_reason?: T;
  receipt_url?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "promotions_select".
 */
export interface PromotionsSelect<T extends boolean = true> {
  id?: T;
  title?: T;
  description?: T;
  discount_type?: T;
  discount_value?: T;
  min_purchase_amount?: T;
  max_discount_amount?: T;
  promo_code?: T;
  start_date?: T;
  end_date?: T;
  usage_limit?: T;
  usage_count?: T;
  applicable_services?: T;
  is_active?: T;
  is_featured?: T;
  image_url?: T;
  terms_conditions?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "inventory-items_select".
 */
export interface InventoryItemsSelect<T extends boolean = true> {
  id?: T;
  name?: T;
  description?: T;
  category?: T;
  sku?: T;
  barcode?: T;
  unit_of_measure?: T;
  unit_cost?: T;
  current_stock?: T;
  minimum_stock?: T;
  maximum_stock?: T;
  reorder_point?: T;
  supplier?: T;
  supplier_contact?: T;
  storage_location?: T;
  expiry_tracking?: T;
  is_active?: T;
  created_by?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "blog-posts_select".
 */
export interface BlogPostsSelect<T extends boolean = true> {
  id?: T;
  author_id?: T;
  title?: T;
  slug?: T;
  excerpt?: T;
  content?: T;
  featured_image?: T;
  gallery_images?: T;
  category?: T;
  tags?: T;
  difficulty?: T;
  read_time_minutes?: T;
  view_count?: T;
  like_count?: T;
  is_published?: T;
  is_featured?: T;
  published_at?: T;
  seo_title?: T;
  seo_description?: T;
  seo_keywords?: T;
  language?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "cms-pages_select".
 */
export interface CmsPagesSelect<T extends boolean = true> {
  id?: T;
  slug?: T;
  title?: T;
  content?: T;
  excerpt?: T;
  meta_title?: T;
  meta_description?: T;
  meta_keywords?: T;
  featured_image_url?: T;
  is_published?: T;
  publish_at?: T;
  page_type?: T;
  template?: T;
  sort_order?: T;
  view_count?: T;
  created_by?: T;
  updated_by?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "marketing-campaigns_select".
 */
export interface MarketingCampaignsSelect<T extends boolean = true> {
  id?: T;
  name?: T;
  description?: T;
  campaign_type?: T;
  target_segment?: T;
  email_template_id?: T;
  subject_line?: T;
  content?: T;
  sender_name?: T;
  sender_email?: T;
  scheduled_at?: T;
  started_at?: T;
  completed_at?: T;
  status?: T;
  total_recipients?: T;
  sent_count?: T;
  delivered_count?: T;
  opened_count?: T;
  clicked_count?: T;
  unsubscribed_count?: T;
  bounced_count?: T;
  budget_amount?: T;
  actual_cost?: T;
  roi_percentage?: T;
  created_by?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "reviews_select".
 */
export interface ReviewsSelect<T extends boolean = true> {
  patient?: T;
  doctor?: T;
  service?: T;
  appointment?: T;
  rating?: T;
  title?: T;
  comment?: T;
  is_anonymous?: T;
  is_approved?: T;
  is_featured?: T;
  helpful_count?: T;
  before_photo?: T;
  after_photo?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "analytics-events_select".
 */
export interface AnalyticsEventsSelect<T extends boolean = true> {
  id?: T;
  event_type?: T;
  event_category?: T;
  event_data?: T;
  patient_id?: T;
  session_id?: T;
  page_url?: T;
  referrer_url?: T;
  ip_address?: T;
  user_agent?: T;
  device_type?: T;
  browser?: T;
  os?: T;
  country?: T;
  city?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "kpi-metrics_select".
 */
export interface KpiMetricsSelect<T extends boolean = true> {
  id?: T;
  date?: T;
  metric_name?: T;
  metric_value?: T;
  metric_unit?: T;
  category?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}