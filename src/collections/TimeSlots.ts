import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const TimeSlots: CollectionConfig = {
  slug: 'time-slots',
  labels: {
    singular: 'Time Slot',
    plural: 'Time Slots',
  },
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['id', 'start_time', 'end_time', 'doctor_id', 'is_available'],
    group: 'Healthcare',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('time_slots'),
}
