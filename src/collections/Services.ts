import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const Services: CollectionConfig = {
  slug: 'services',
  labels: {
    singular: 'Service',
    plural: 'Services',
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'category', 'price', 'duration', 'status'],
    group: 'Healthcare',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('services'),
}
