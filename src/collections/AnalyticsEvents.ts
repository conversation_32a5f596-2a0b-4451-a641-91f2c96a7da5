import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const AnalyticsEvents: CollectionConfig = {
  slug: 'analytics-events',
  labels: {
    singular: 'Analytics Event',
    plural: 'Analytics Events',
  },
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['event_name', 'user_id', 'event_type', 'timestamp'],
    group: 'Analytics',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('analytics_events'),
}
