import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const CmsPages: CollectionConfig = {
  slug: 'cms-pages',
  labels: {
    singular: 'CMS Page',
    plural: 'CMS Pages',
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'slug', 'status', 'template', 'published_at'],
    group: 'Content',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('cms_pages'),
}
