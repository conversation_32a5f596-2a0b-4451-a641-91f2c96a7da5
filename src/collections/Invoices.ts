import { CollectionConfig } from 'payload'

export const Invoices: CollectionConfig = {
  slug: 'invoices',
  labels: {
    singular: 'Invoice',
    plural: 'Invoices',
  },
  admin: {
    useAsTitle: 'invoice_number',
    defaultColumns: ['invoice_number', 'appointment', 'total_amount', 'is_paid', 'due_date'],
    group: 'Business',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    {
      name: 'appointment',
      type: 'relationship',
      relationTo: 'appointments',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'payment',
      type: 'relationship',
      relationTo: 'payments',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'invoice_number',
      label: 'Invoice Number',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'issue_date',
      label: 'Issue Date',
      type: 'date',
      required: false,
      defaultValue: () => new Date().toISOString(),
    },
    {
      name: 'due_date',
      label: 'Due Date',
      type: 'date',
      required: false,
    },
    {
      name: 'subtotal',
      type: 'number',
      required: true,
      admin: {
        step: 0.01,
      },
    },
    {
      name: 'tax_amount',
      label: 'Tax Amount',
      type: 'number',
      required: false,
      admin: {
        step: 0.01,
      },
    },
    {
      name: 'discount_amount',
      label: 'Discount Amount',
      type: 'number',
      required: false,
      admin: {
        step: 0.01,
      },
    },
    {
      name: 'total_amount',
      label: 'Total Amount',
      type: 'number',
      required: true,
      admin: {
        step: 0.01,
      },
    },
    {
      name: 'currency',
      type: 'select',
      required: false,
      defaultValue: 'USD',
      options: [
        { label: 'USD', value: 'USD' },
        { label: 'EUR', value: 'EUR' },
        { label: 'GBP', value: 'GBP' },
        { label: 'THB', value: 'THB' },
      ],
    },
    {
      name: 'notes',
      type: 'textarea',
      required: false,
    },
    {
      name: 'is_paid',
      label: 'Paid',
      type: 'checkbox',
      required: false,
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'pdf_url',
      label: 'PDF URL',
      type: 'text',
      required: false,
      admin: {
        readOnly: true,
      },
    },
  ],
}
