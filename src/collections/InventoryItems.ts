import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const InventoryItems: CollectionConfig = {
  slug: 'inventory-items',
  labels: {
    singular: 'Inventory Item',
    plural: 'Inventory Items',
  },
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['name', 'sku', 'category', 'quantity', 'unit_price'],
    group: 'Business',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('inventory_items'),
}
