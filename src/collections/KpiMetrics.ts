import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const KpiMetrics: CollectionConfig = {
  slug: 'kpi-metrics',
  labels: {
    singular: 'KPI Metric',
    plural: 'KPI Metrics',
  },
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['metric_name', 'metric_value', 'period', 'date'],
    group: 'Analytics',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('kpi_metrics'),
}
