import { CollectionConfig } from 'payload'

export const Payments: CollectionConfig = {
  slug: 'payments',
  labels: {
    singular: 'Payment',
    plural: 'Payments',
  },
  admin: {
    useAsTitle: 'transaction_id',
    defaultColumns: ['appointment', 'patient', 'amount', 'payment_status', 'payment_date'],
    group: 'Business',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    {
      name: 'appointment',
      type: 'relationship',
      relationTo: 'appointments',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'user-profiles',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'amount',
      type: 'number',
      required: true,
      admin: {
        step: 0.01,
      },
    },
    {
      name: 'payment_method',
      label: 'Payment Method',
      type: 'select',
      required: true,
      options: [
        { label: 'Credit Card', value: 'credit_card' },
        { label: 'Debit Card', value: 'debit_card' },
        { label: 'Cash', value: 'cash' },
        { label: 'Bank Transfer', value: 'bank_transfer' },
        { label: 'Digital Wallet', value: 'digital_wallet' },
        { label: 'Insurance', value: 'insurance' },
      ],
    },
    {
      name: 'payment_status',
      label: 'Payment Status',
      type: 'select',
      required: false,
      defaultValue: 'pending',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Processing', value: 'processing' },
        { label: 'Completed', value: 'completed' },
        { label: 'Failed', value: 'failed' },
        { label: 'Cancelled', value: 'cancelled' },
        { label: 'Refunded', value: 'refunded' },
        { label: 'Partially Refunded', value: 'partially_refunded' },
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'transaction_id',
      label: 'Transaction ID',
      type: 'text',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'stripe_payment_intent_id',
      label: 'Stripe Payment Intent ID',
      type: 'text',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'payment_date',
      label: 'Payment Date',
      type: 'date',
      required: false,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'refund_amount',
      label: 'Refund Amount',
      type: 'number',
      required: false,
      admin: {
        step: 0.01,
      },
    },
    {
      name: 'refund_date',
      label: 'Refund Date',
      type: 'date',
      required: false,
    },
    {
      name: 'refund_reason',
      label: 'Refund Reason',
      type: 'textarea',
      required: false,
    },
    {
      name: 'receipt_url',
      label: 'Receipt URL',
      type: 'text',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'metadata',
      type: 'json',
      required: false,
      admin: {
        readOnly: true,
      },
    },
  ],
}
