import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const AdminProfiles: CollectionConfig = {
  slug: 'admin-profiles',
  labels: {
    singular: 'Admin Profile',
    plural: 'Admin Profiles',
  },
  admin: {
    useAsTitle: 'first_name',
    defaultColumns: ['first_name', 'last_name', 'email', 'role'],
    group: 'Users',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('admin_profiles'),
}
