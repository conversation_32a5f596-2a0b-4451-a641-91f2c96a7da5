import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const Doctors: CollectionConfig = {
  slug: 'doctors',
  labels: {
    singular: 'Doctor',
    plural: 'Doctors',
  },
  admin: {
    useAsTitle: 'license_number',
    defaultColumns: ['license_number', 'specialization', 'qualification', 'rating', 'is_available'],
    group: 'Healthcare',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('doctors'),
}
