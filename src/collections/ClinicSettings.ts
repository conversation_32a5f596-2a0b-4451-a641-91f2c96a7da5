import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const ClinicSettings: CollectionConfig = {
  slug: 'clinic-settings',
  labels: {
    singular: 'Clinic Setting',
    plural: 'Clinic Settings',
  },
  admin: {
    useAsTitle: 'key',
    defaultColumns: ['key', 'value', 'description', 'data_type'],
    group: 'Configuration',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('clinic_settings'),
}
