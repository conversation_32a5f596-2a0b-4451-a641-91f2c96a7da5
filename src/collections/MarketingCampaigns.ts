import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const MarketingCampaigns: CollectionConfig = {
  slug: 'marketing-campaigns',
  labels: {
    singular: 'Marketing Campaign',
    plural: 'Marketing Campaigns',
  },
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['name', 'type', 'status', 'start_date', 'end_date'],
    group: 'Marketing',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('marketing_campaigns'),
}
