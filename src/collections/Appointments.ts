import { CollectionConfig } from 'payload'

export const Appointments: CollectionConfig = {
  slug: 'appointments',
  labels: {
    singular: 'Appointment',
    plural: 'Appointments',
  },
  admin: {
    useAsTitle: 'appointment_date',
    defaultColumns: ['patient', 'doctor', 'service', 'appointment_date', 'status'],
    group: 'Healthcare',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'user-profiles',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'doctor',
      type: 'relationship',
      relationTo: 'doctors',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'service',
      type: 'relationship',
      relationTo: 'services',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'timeSlot',
      label: 'Time Slot',
      type: 'relationship',
      relationTo: 'time-slots',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'appointment_date',
      label: 'Appointment Date',
      type: 'date',
      required: true,
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'duration_minutes',
      label: 'Duration (Minutes)',
      type: 'number',
      required: false,
    },
    {
      name: 'status',
      type: 'select',
      required: false,
      defaultValue: 'scheduled',
      options: [
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'Confirmed', value: 'confirmed' },
        { label: 'In Progress', value: 'in_progress' },
        { label: 'Completed', value: 'completed' },
        { label: 'Cancelled', value: 'cancelled' },
        { label: 'No Show', value: 'no_show' },
      ],
    },
    {
      name: 'total_amount',
      label: 'Total Amount',
      type: 'number',
      required: true,
    },
    {
      name: 'deposit_amount',
      label: 'Deposit Amount',
      type: 'number',
      required: false,
    },
    {
      name: 'patient_notes',
      label: 'Patient Notes',
      type: 'textarea',
      required: false,
    },
    {
      name: 'doctor_notes',
      label: 'Doctor Notes',
      type: 'textarea',
      required: false,
    },
    {
      name: 'treatment_plan',
      label: 'Treatment Plan',
      type: 'richText',
      required: false,
    },
    {
      name: 'before_photos',
      label: 'Before Photos',
      type: 'upload',
      relationTo: 'media',
      hasMany: true,
      required: false,
    },
    {
      name: 'after_photos',
      label: 'After Photos',
      type: 'upload',
      relationTo: 'media',
      hasMany: true,
      required: false,
    },
    {
      name: 'prescription',
      type: 'richText',
      required: false,
    },
    {
      name: 'next_appointment_recommended',
      label: 'Next Appointment Recommended',
      type: 'checkbox',
      required: false,
    },
    {
      name: 'followup_date',
      label: 'Follow-up Date',
      type: 'date',
      required: false,
    },
    {
      name: 'confirmation_sent_at',
      label: 'Confirmation Sent At',
      type: 'date',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'reminder_sent_at',
      label: 'Reminder Sent At',
      type: 'date',
      required: false,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'checked_in_at',
      label: 'Checked In At',
      type: 'date',
      required: false,
    },
    {
      name: 'completed_at',
      label: 'Completed At',
      type: 'date',
      required: false,
    },
    {
      name: 'cancelled_at',
      label: 'Cancelled At',
      type: 'date',
      required: false,
    },
    {
      name: 'cancellation_reason',
      label: 'Cancellation Reason',
      type: 'textarea',
      required: false,
    },
    {
      name: 'patient_phone',
      label: 'Patient Phone',
      type: 'text',
      required: false,
    },
    {
      name: 'patient_email',
      label: 'Patient Email',
      type: 'email',
      required: false,
    },
  ],
}
