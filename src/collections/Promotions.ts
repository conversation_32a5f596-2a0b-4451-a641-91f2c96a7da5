import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const Promotions: CollectionConfig = {
  slug: 'promotions',
  labels: {
    singular: 'Promotion',
    plural: 'Promotions',
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'discount_type', 'discount_value', 'start_date', 'end_date'],
    group: 'Business',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('promotions'),
}
