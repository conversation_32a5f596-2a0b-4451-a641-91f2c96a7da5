import { CollectionConfig } from 'payload'
import { getDatabaseFields } from '../lib/supabaseAdapter'

export const UserProfiles: CollectionConfig = {
  slug: 'user-profiles',
  labels: {
    singular: 'User Profile',
    plural: 'User Profiles',
  },
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['first_name', 'last_name', 'email', 'phone', 'role', 'is_active'],
    group: 'Users',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: getDatabaseFields('user_profiles'),
}
