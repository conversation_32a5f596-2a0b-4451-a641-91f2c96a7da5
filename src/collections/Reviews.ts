import { CollectionConfig } from 'payload'

export const Reviews: CollectionConfig = {
  slug: 'reviews',
  labels: {
    singular: 'Review',
    plural: 'Reviews',
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['patient', 'doctor', 'service', 'rating', 'is_approved'],
    group: 'Analytics',
  },
  access: {
    read: () => true,
    create: () => true,
    update: () => true,
    delete: () => true,
  },
  fields: [
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'user-profiles',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'doctor',
      type: 'relationship',
      relationTo: 'doctors',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'service',
      type: 'relationship',
      relationTo: 'services',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'appointment',
      type: 'relationship',
      relationTo: 'appointments',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'rating',
      type: 'number',
      required: true,
      min: 1,
      max: 5,
      admin: {
        step: 1,
      },
    },
    {
      name: 'title',
      type: 'text',
      required: false,
    },
    {
      name: 'comment',
      type: 'textarea',
      required: false,
    },
    {
      name: 'is_anonymous',
      label: 'Anonymous Review',
      type: 'checkbox',
      required: false,
      defaultValue: false,
    },
    {
      name: 'is_approved',
      label: 'Approved',
      type: 'checkbox',
      required: false,
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'is_featured',
      label: 'Featured Review',
      type: 'checkbox',
      required: false,
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'helpful_count',
      label: 'Helpful Count',
      type: 'number',
      required: false,
      defaultValue: 0,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'before_photo',
      label: 'Before Photo',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'after_photo',
      label: 'After Photo',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
  ],
}
